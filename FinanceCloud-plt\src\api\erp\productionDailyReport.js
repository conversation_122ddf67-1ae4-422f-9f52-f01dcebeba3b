import request from '@/router/axios'

/**
 * 获取生产日报数据
 * @param {Object} params 查询参数
 * @param {String} type 类型：'detail' 或 'summary'
 * @returns {Promise}
 */
export function getTableListData(params, type = 'detail') {
  const url = type === 'summary'
    ? '/flowable/api/production/work-reports/daily-summary'
    : '/flowable/api/production/work-reports/daily-report';

  return request({
    url: url,
    method: 'get',
    params: params
  })
}

/**
 * 导出生产日报
 * @param {Object} params 查询参数
 * @param {String} format 导出格式：excel/pdf
 * @returns {Promise}
 */
export function exportExcel(params, format = 'excel') {
  return request({
    url: '/flowable/api/production/work-reports/export',
    method: 'get',
    params: { ...params, format },
    responseType: 'blob'
  })
}

/**
 * 打印生产日报
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function printReport(params) {
  return request({
    url: '/flowable/api/production/work-reports/print',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
