package com.lg.produce.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.lg.common.util.SequenceUtils;
import com.lg.engineering.mapper.ProcessRouteOperationMapper;
import com.lg.engineering.service.OperationMoldUsageService;
import com.lg.engineering.service.ProcessRouteOperationService;
import com.lg.engineering.service.ProcessRouteService;
import com.lg.engineering.vo.ProcessRouteOperationVO;
import com.lg.engineering.vo.ProcessRouteVO;
import com.lg.financecloud.common.core.util.R;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import com.lg.financecloud.common.security.util.SecurityUtils;
import com.lg.produce.entity.*;
import com.lg.produce.mapper.*;
import com.lg.produce.service.ErpTaskReportService;
import com.lg.produce.service.ErpWipTransferService;
import com.lg.produce.vo.ErpTaskReportEquipmentVO;
import com.lg.produce.vo.ErpTaskReportStaffVO;
import com.lg.produce.vo.ErpTaskReportVO;
import com.lg.produce.vo.ProductionDailyReportVO;
import com.lg.produce.vo.ProductionDailySummaryVO;
import com.lg.produce.vo.ProductionPlanExecutionVO;
import com.lg.produce.vo.ProductionPlanExecutionSummaryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

// 新的报表服务导入
import com.lg.financecloud.common.report.SimpleReportService;
import com.lg.financecloud.common.report.dto.ReportInfo;
import com.lg.financecloud.common.report.ReportInfoBuilder;
import com.lg.financecloud.common.security.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 工序任务报工 服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ErpTaskReportServiceImpl implements ErpTaskReportService {

    private final ErpTaskReportMapper taskReportMapper;
    private final ErpProcessTaskMapper processTaskMapper;
    private final ErpTaskReportEquipmentMapper equipmentMapper;
    private final ErpTaskReportStaffMapper staffMapper;
    private final OperationMoldUsageService operationMoldUsageService;
    private final ErpWipTransferService erpWipTransferService;
    private final ProcessRouteService processRouteService;
    private final ErpMachinHeaderMapper erpMachinHeaderMapper;

    @Autowired(required = false)
    private SimpleReportService simpleReportService;


    @Override
    public IPage<ErpTaskReportVO> pageList(Page<ErpTaskReport> page, ErpTaskReportVO queryVO) {
        // 提取查询参数
        String workOrderId = queryVO != null ? queryVO.getWorkOrderId() : null;
        String taskId = queryVO != null ? queryVO.getTaskId() : null;
        String workCenterId = queryVO != null ? queryVO.getWorkCenterId() : null;
        String startDate = null; // 从queryVO中获取日期范围
        String endDate = null;   // 从queryVO中获取日期范围
        
        // 调用Mapper进行分页查询
        return taskReportMapper.selectReportPage(page, workOrderId, taskId, workCenterId, startDate, endDate);
    }

    @Override
    public ErpTaskReportVO getDetailById(String id) {
        // 查询报工主信息
        ErpTaskReportVO reportVO = taskReportMapper.selectReportDetail(id);
        if (reportVO == null) {
            return null;
        }
        
        // 查询设备工时列表和人员工时列表应该调用其他模块中已有的服务
        // 这里暂时返回空列表
        reportVO.setEquipmentList(new ArrayList<>());
        reportVO.setStaffList(new ArrayList<>());
        
        return reportVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ErpTaskReportVO> createTaskReport(ErpTaskReportVO reportVO) {
        // 1. 检查任务是否存在
        ErpProcessTask task = processTaskMapper.selectById(reportVO.getTaskId());
        if (task == null) {
            return R.failed("任务不存在");
        }
        
        // 2. 检查工作中心是否存在
        String workCenterName = taskReportMapper.selectWorkCenterNameById(reportVO.getWorkCenterId());
//        if (StrUtil.isBlank(workCenterName)) {
//            return R.failed("工作中心不存在");
//        }
//
        // 3. 检查完成数量是否合法
        if (reportVO.getCompletedQty() == null || reportVO.getCompletedQty().compareTo(BigDecimal.ZERO) <= 0) {
            return R.failed("完成数量必须大于0");
        }
        
        // 4. 检查合格数量是否合法
        if (reportVO.getQualifiedQty() == null || reportVO.getQualifiedQty().compareTo(BigDecimal.ZERO) < 0) {
            return R.failed("合格数量不能为负数");
        }
        
        // 5. 检查合格数量是否小于等于完成数量
        if (reportVO.getQualifiedQty().compareTo(reportVO.getCompletedQty()) > 0) {
            return R.failed("合格数量不能大于完成数量");
        }
        
        // 6. 时间区间校验：检查在指定时间区间内是否已有报工记录
        if (!validateTaskReportTimeConflict(reportVO.getStartTime(), reportVO.getEndTime(), reportVO.getWorkCenterId())) {
            return R.failed("在指定时间区间内已有报工记录，请检查时间设置");
        }
        
        // 7. 创建报工记录
        ErpTaskReport report = new ErpTaskReport();
        BeanUtil.copyProperties(reportVO, report);
        report.setId(SequenceUtils.nextIdStr());
        report.setTenantId(TenantContextHolder.getTenantId());
        report.setWorkOrderId(task.getWorkOrderId());
        report.setWorkCenterName(workCenterName);
        report.setReportTime(LocalDateTime.now());
        report.setDefectiveQty(reportVO.getCompletedQty().subtract(reportVO.getQualifiedQty()));
        report.setOperationId(task.getProcessId());
//        report.setDefectiveQty(reportVO.getCompletedQty().subtract(reportVO.getQualifiedQty()));
        report.setCreateTime(LocalDateTime.now());
        report.setCreateBy(SecurityUtils.getUser().getStaffInfo().getId());
        // 8. 保存报工记录
        taskReportMapper.insert(report);

        
        // 9. 保存设备工时记录和人员工时记录

            // 9.1 保存设备工时记录
            if (CollUtil.isNotEmpty(reportVO.getEquipmentList())) {
                List<ErpTaskReportEquipment> equipmentList = buildEquipmentRecords(report.getId(), reportVO.getEquipmentList());
                if (CollUtil.isNotEmpty(equipmentList)) {
                    equipmentMapper.batchInsert(equipmentList);
                    report.setEquipmentList(equipmentList);//将设备报告数据写入报告实体
                }
            }
            
            // 9.2 保存人员工时记录
            if (CollUtil.isNotEmpty(reportVO.getStaffList())) {
                List<ErpTaskReportStaff> staffList = buildStaffRecords(report.getId(), reportVO.getStaffList());
                if (CollUtil.isNotEmpty(staffList)) {
                    staffMapper.batchInsert(staffList);
                }
            }




        // 10. 创建模具使用记录和在制品流转
        //这里还需要实现以下，工序模具使用记录
        List<ErpTaskReport> erpTaskReports = new ArrayList<>();
        erpTaskReports.add(report);
        operationMoldUsageService.batchInsertUsage4TaskReport(erpTaskReports);
        
        // 11. 处理返工单报工的特殊逻辑
        if (isReworkProcessTask(task)) {
            // 返工工序：构造源工序流向下一工序的在制流转记录
            List<ErpWipTransfer> transfers = buildReworkWipTransfer(report, task);
            erpWipTransferService.batchAddTransfer(transfers);
        } else {
            // 正常工序：构造当前工序流向下一工序的在制流转记录
            List<ErpWipTransfer> transfers = buildErpWipTransfer(report);
            erpWipTransferService.batchAddTransfer(transfers);
        }

        // 12. 返回创建结果
        return R.ok();
    }


    /**
     * 构建返工工序的在制流转记录
     * 返工完成后，根据工序状态判断是否构造源工序流向下一工序的流转，同时生成返工单的流转记录
     */
    private List<ErpWipTransfer> buildReworkWipTransfer(ErpTaskReport report, ErpProcessTask reworkTask) {
        try {
            ArrayList<ErpWipTransfer> transfers = new ArrayList<>();
            
            // 1. 获取返工单信息
            ErpMachinHeader reworkOrder = erpMachinHeaderMapper.selectById(reworkTask.getWorkOrderId());
            if (reworkOrder == null) {
                log.error("返工单不存在，返工单ID: {}", reworkTask.getWorkOrderId());
                return transfers;
            }

            // 2. 获取源工序任务信息
            String originalTaskId = reworkOrder.getOriginalTaskId();
            if (StrUtil.isBlank(originalTaskId)) {
                log.error("返工单缺少原工序任务ID，返工单ID: {}", reworkOrder.getId());
                return transfers;
            }

            ErpProcessTask originalTask = processTaskMapper.selectById(originalTaskId);
            if (originalTask == null) {
                log.error("原工序任务不存在，原工序任务ID: {}", originalTaskId);
                return transfers;
            }

            // 3. 判断当前返工工序是否为返工单的最后一个工序
            boolean isLastReworkProcess = isLastReworkProcess(reworkTask, reworkOrder.getId());
            
            // 4. 如果是最后工序，构造源工序流向下一工序的在制流转记录
            if (isLastReworkProcess) {
                // 获取源工序的工艺路线信息，确定下一工序
                ProcessRouteVO routeDetail = processRouteService.getRouteDetail(originalTask.getRouteId());
                List<ProcessRouteOperationVO> operations = routeDetail.getOperations();

                // 找到源工序在工艺路线中的位置
                int sourceIndex = -1;
                for (int i = 0; i < operations.size(); i++) {
                    if (operations.get(i).getOperationId().equals(originalTask.getProcessId())) {
                        sourceIndex = i;
                        break;
                    }
                }

                // 确定下一工序ID
                String nextOperationId = null;
                if (sourceIndex >= 0 && sourceIndex < operations.size() - 1) {
                    nextOperationId = operations.get(sourceIndex + 1).getOperationId();
                }

                // 构造源工序到下一工序的在制流转记录
                if (StrUtil.isNotBlank(nextOperationId)) {
                    ErpWipTransfer sourceTransfer = new ErpWipTransfer();
                    sourceTransfer.setId(SequenceUtils.nextIdStr());
                    sourceTransfer.setCreateBy(SecurityUtils.getUser().getStaffInfo().getId());
                    sourceTransfer.setDelFlag("0");
                    sourceTransfer.setTenantId(report.getTenantId());
                    sourceTransfer.setCreateTime(LocalDateTime.now());
                    sourceTransfer.setProductionOrderId(originalTask.getWorkOrderId()); // 源工单ID
                    sourceTransfer.setFromOperationId(originalTask.getProcessId());     // 从源工序
                    sourceTransfer.setToOperationId(nextOperationId);                  // 到下一工序
                    sourceTransfer.setMaterialId(reworkOrder.getMaterialId());         // 物料ID
                    sourceTransfer.setNormsId(reworkOrder.getNormsId());               // 规格ID
                    sourceTransfer.setOperatorId(SecurityUtils.getUser().getStaffInfo().getId());
                    sourceTransfer.setQuantity(report.getQualifiedQty());              // 流转数量为返工合格数量
                    sourceTransfer.setWorkReportId(report.getId());
                    sourceTransfer.setTransferTime(report.getReportTime());
                    sourceTransfer.setTransferType("REWORK");                          // 标识为返工流转

                    transfers.add(sourceTransfer);
                    
                    log.info("返工最后工序报工完成，构造源工序[{}]到下一工序[{}]的流转记录，数量: {}",
                            originalTask.getProcessId(), nextOperationId, sourceTransfer.getQuantity());
                } else {
                    log.info("返工最后工序报工完成，源工序[{}]已是最后工序，无需构造流转记录",
                            originalTask.getProcessId());
                }
            } else {
                log.info("当前返工工序[{}]非最后工序，无需构造源工序流转记录", reworkTask.getProcessId());
            }
            
            // 5. 生成当前返工单的流转记录
            ErpWipTransfer reworkTransfer = buildCurrentReworkTransfer(report, reworkTask, reworkOrder);
            if (reworkTransfer != null) {
                transfers.add(reworkTransfer);
                log.info("生成返工单流转记录，从工序[{}]，数量: {}", 
                        reworkTask.getProcessId(), reworkTransfer.getQuantity());
            }

            return transfers;

        } catch (Exception e) {
            log.error("构造返工在制流转记录时发生错误", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 判断当前返工工序是否为返工单的最后一个工序
     */
    private boolean isLastReworkProcess(ErpProcessTask currentReworkTask, String reworkOrderId) {
        try {
            // 查询返工单的所有工序任务，按序号排序
            List<ErpProcessTask> reworkTasks = processTaskMapper.selectList(
                new LambdaQueryWrapper<ErpProcessTask>()
                    .eq(ErpProcessTask::getWorkOrderId, reworkOrderId)
                    .eq(ErpProcessTask::getIsReworkProcess, "1")
                    .orderByAsc(ErpProcessTask::getSequenceNo)
            );
            
            if (reworkTasks.isEmpty()) {
                log.warn("未找到返工单[{}]的工序任务", reworkOrderId);
                return false;
            }
            
            // 获取最后一个工序任务
            ErpProcessTask lastTask = reworkTasks.get(reworkTasks.size() - 1);
            
            // 判断当前工序是否为最后工序
            boolean isLast = currentReworkTask.getId().equals(lastTask.getId());
            
            log.debug("返工单[{}]工序判断：当前工序[{}]序号[{}]，最后工序[{}]序号[{}]，是否最后工序：{}",
                    reworkOrderId, currentReworkTask.getProcessId(), currentReworkTask.getSequenceNo(),
                    lastTask.getProcessId(), lastTask.getSequenceNo(), isLast);
            
            return isLast;
            
        } catch (Exception e) {
            log.error("判断返工工序是否为最后工序时发生错误，返工单ID: {}, 当前工序ID: {}", 
                    reworkOrderId, currentReworkTask.getId(), e);
            return false;
        }
    }
    
    /**
     * 构建当前返工单的流转记录
     */
    private ErpWipTransfer buildCurrentReworkTransfer(ErpTaskReport report, ErpProcessTask reworkTask, ErpMachinHeader reworkOrder) {
        try {
            // 查询返工单的所有工序任务，按序号排序
            List<ErpProcessTask> reworkTasks = processTaskMapper.selectList(
                new LambdaQueryWrapper<ErpProcessTask>()
                    .eq(ErpProcessTask::getWorkOrderId, reworkTask.getWorkOrderId())
                    .eq(ErpProcessTask::getIsReworkProcess, "1")
                    .orderByAsc(ErpProcessTask::getSequenceNo)
            );
            
            // 找到当前工序在返工单工序任务中的位置
            int currentIndex = -1;
            for (int i = 0; i < reworkTasks.size(); i++) {
                if (reworkTasks.get(i).getId().equals(reworkTask.getId())) {
                    currentIndex = i;
                    break;
                }
            }
            
            // 确定下一返工工序ID（基于返工单的工序任务序列）
            String nextOperationId = null;
            if (currentIndex >= 0 && currentIndex < reworkTasks.size() - 1) {
                ErpProcessTask nextTask = reworkTasks.get(currentIndex + 1);
                nextOperationId = nextTask.getProcessId();
            }
            
            // 构造返工单流转记录
            ErpWipTransfer reworkTransfer = new ErpWipTransfer();
            reworkTransfer.setId(SequenceUtils.nextIdStr());
            reworkTransfer.setCreateBy(SecurityUtils.getUser().getStaffInfo().getId());
            reworkTransfer.setDelFlag("0");
            reworkTransfer.setTenantId(report.getTenantId());
            reworkTransfer.setCreateTime(LocalDateTime.now());
            reworkTransfer.setProductionOrderId(reworkTask.getWorkOrderId()); // 返工单ID
            reworkTransfer.setFromOperationId(reworkTask.getProcessId());     // 从当前返工工序
            reworkTransfer.setToOperationId(nextOperationId);                // 到下一返工工序（如果存在）
            reworkTransfer.setMaterialId(reworkOrder.getMaterialId());       // 物料ID
            reworkTransfer.setNormsId(reworkOrder.getNormsId());             // 规格ID
            reworkTransfer.setOperatorId(SecurityUtils.getUser().getStaffInfo().getId());
            reworkTransfer.setQuantity(report.getQualifiedQty());            // 流转数量为返工合格数量
            reworkTransfer.setWorkReportId(report.getId());
            reworkTransfer.setTransferTime(report.getReportTime());
            reworkTransfer.setTransferType("REWORK_INTERNAL");               // 标识为返工内部流转
            
            log.debug("返工单流转记录构建：当前工序[{}]序号[{}]，下一工序[{}]，数量: {}",
                    reworkTask.getProcessId(), reworkTask.getSequenceNo(), nextOperationId, reworkTransfer.getQuantity());
            
            return reworkTransfer;
            
        } catch (Exception e) {
            log.error("构建返工单流转记录时发生错误，返工单ID: {}, 工序ID: {}", 
                    reworkTask.getWorkOrderId(), reworkTask.getProcessId(), e);
            return null;
        }
    }

    private List<ErpWipTransfer> buildErpWipTransfer(ErpTaskReport report) {
        //查询工单信息
        ErpMachinHeader header = erpMachinHeaderMapper.selectById(report.getWorkOrderId());
        ErpProcessTask erpProcessTask = processTaskMapper.selectById(report.getTaskId());
        //根据工艺路线查询工序信息
        ProcessRouteVO routeDetail = processRouteService.getRouteDetail(erpProcessTask.getRouteId());
        List<ProcessRouteOperationVO> operations = routeDetail.getOperations();
        String currentOperationId = report.getOperationId();
        int index = 0;
        String toOperationId= null;//下个工序的id
        for (ProcessRouteOperationVO operation : operations) {
            if (operation.getOperationId().equals(currentOperationId)){
                 index = operations.indexOf(operation);
                 break;
            }
        }
        if (index<operations.size()-1){
            toOperationId = operations.get(index + 1).getOperationId();
        }



        ArrayList<ErpWipTransfer> erpWipTransfers = new ArrayList<>();
        ErpWipTransfer erpWipTransfer = new ErpWipTransfer();
        erpWipTransfer.setId(SequenceUtils.nextIdStr());
        erpWipTransfer.setCreateBy(SecurityUtils.getUser().getStaffInfo().getId());
        erpWipTransfer.setDelFlag("0");
        erpWipTransfer.setTenantId(report.getTenantId());
        erpWipTransfer.setCreateTime(LocalDateTime.now());
        erpWipTransfer.setProductionOrderId(report.getWorkOrderId());
        erpWipTransfer.setFromOperationId(report.getOperationId());
        erpWipTransfer.setToOperationId(toOperationId);
        erpWipTransfer.setMaterialId(header.getMaterialId());
        erpWipTransfer.setNormsId(header.getNormsId());
        erpWipTransfer.setOperatorId(SecurityUtils.getUser().getStaffInfo().getId());
        erpWipTransfer.setQuantity(report.getQualifiedQty());//合格数量
        erpWipTransfer.setWorkReportId(report.getId());
        erpWipTransfer.setTransferTime(report.getReportTime());
        erpWipTransfers.add(erpWipTransfer);

        return erpWipTransfers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ErpTaskReportVO> updateTaskReport(ErpTaskReportVO reportVO) {
        // 1. 检查报工记录是否存在
        ErpTaskReport oldReport = taskReportMapper.selectById(reportVO.getId());
        if (oldReport == null) {
            return R.failed("报工记录不存在");
        }
        // 2. 基础校验
        if (reportVO.getCompletedQty() == null || reportVO.getCompletedQty().compareTo(BigDecimal.ZERO) <= 0) {
            return R.failed("完成数量必须大于0");
        }
        if (reportVO.getQualifiedQty() == null || reportVO.getQualifiedQty().compareTo(BigDecimal.ZERO) < 0) {
            return R.failed("合格数量不能为负数");
        }
        if (reportVO.getQualifiedQty().compareTo(reportVO.getCompletedQty()) > 0) {
            return R.failed("合格数量不能大于完成数量");
        }

        // 3. 先删：回滚旧流转、删除旧子表与主表
        erpWipTransferService.delTransferByTaskReportId(java.util.Collections.singletonList(oldReport.getId()));
        equipmentMapper.deleteByReportId(oldReport.getId());
        staffMapper.deleteByReportId(oldReport.getId());
        taskReportMapper.deleteById(oldReport.getId());

        // 4. 后增：重建主表 + 子表 + 流转
        ErpProcessTask task = processTaskMapper.selectById(reportVO.getTaskId());
        if (task == null) {
            return R.failed("任务不存在");
        }
        String newReportId = SequenceUtils.nextIdStr();
        String workCenterName = taskReportMapper.selectWorkCenterNameById(reportVO.getWorkCenterId());

        ErpTaskReport newReport = new ErpTaskReport();
        BeanUtil.copyProperties(reportVO, newReport);
        newReport.setId(newReportId);
        newReport.setTenantId(TenantContextHolder.getTenantId());
        newReport.setWorkOrderId(task.getWorkOrderId());
        newReport.setWorkCenterName(workCenterName);
        newReport.setReportTime(LocalDateTime.now());
        newReport.setDefectiveQty(newReport.getCompletedQty().subtract(newReport.getQualifiedQty()));
        newReport.setOperationId(task.getProcessId());
        newReport.setCreateTime(LocalDateTime.now());
        newReport.setCreateBy(SecurityUtils.getUser().getStaffInfo().getId());
        taskReportMapper.insert(newReport);

        // 设备/人员子表按新reportId重建
        if (CollUtil.isNotEmpty(reportVO.getEquipmentList())) {
            List<ErpTaskReportEquipment> equipmentList = buildEquipmentRecords(newReportId, reportVO.getEquipmentList());
            if (CollUtil.isNotEmpty(equipmentList)) {
                equipmentMapper.batchInsert(equipmentList);
            }
        }
        if (CollUtil.isNotEmpty(reportVO.getStaffList())) {
            List<ErpTaskReportStaff> staffList = buildStaffRecords(newReportId, reportVO.getStaffList());
            if (CollUtil.isNotEmpty(staffList)) {
                staffMapper.batchInsert(staffList);
            }
        }

        // 按是否返工工序构造并添加流转
        if (isReworkProcessTask(task)) {
            List<ErpWipTransfer> transfers = buildReworkWipTransfer(newReport, task);
            erpWipTransferService.batchAddTransfer(transfers);
        } else {
            List<ErpWipTransfer> transfers = buildErpWipTransfer(newReport);
            erpWipTransferService.batchAddTransfer(transfers);
        }

        return R.ok(getDetailById(newReportId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> deleteTaskReport(String id) {
        // 1. 检查报工记录是否存在
        ErpTaskReport report = taskReportMapper.selectById(id);
        if (report == null) {
            return R.failed("报工记录不存在");
        }

        // 3. 删除设备与人员工时子表
        equipmentMapper.deleteByReportId(id);
        staffMapper.deleteByReportId(id);

        // 4. 删除报工主表
        taskReportMapper.deleteById(id);

        // 2. 删除报工主表记录前，先回滚对应的流转与相关联动
        erpWipTransferService.delTransferByTaskReportId(java.util.Collections.singletonList(id));



        return R.ok(true);
    }

    @Override
    public List<ErpTaskReportVO> getReportsByWorkOrderId(String workOrderId) {
        return taskReportMapper.selectReportsByWorkOrderId(workOrderId);
    }

    @Override
    public List<ErpTaskReportVO> getReportsByTaskId(String taskId) {
        return taskReportMapper.selectReportsByTaskId(taskId);
    }

    @Override
    public BigDecimal sumReportedQuantity(String taskId) {
        return taskReportMapper.sumReportedQuantity(taskId);
    }

    @Override
    public R<List<ErpTaskReportEquipmentVO>> getWorkCenterEquipments(String workCenterId) {
        // 这里应该调用其他模块中已有的服务
        // 暂时返回模拟数据
        List<ErpTaskReportEquipmentVO> equipmentList = new ArrayList<>();
        
        ErpTaskReportEquipmentVO equipment1 = new ErpTaskReportEquipmentVO();
        equipment1.setEquipmentId("EQ001");
        equipment1.setEquipmentCode("CNC-001");
        equipment1.setEquipmentName("数控车床1");
        equipment1.setWorkHours(new BigDecimal("2.5"));
        equipment1.setRemark("主加工");
        equipmentList.add(equipment1);
        
        ErpTaskReportEquipmentVO equipment2 = new ErpTaskReportEquipmentVO();
        equipment2.setEquipmentId("EQ002");
        equipment2.setEquipmentCode("CNC-002");
        equipment2.setEquipmentName("数控车床2");
        equipment2.setWorkHours(new BigDecimal("2.0"));
        equipment2.setRemark("辅助");
        equipmentList.add(equipment2);
        
        return R.ok(equipmentList);
    }

    @Override
    public R<List<ErpTaskReportStaffVO>> getWorkCenterStaff(String workCenterId) {
        // TODO: 实现获取工作中心人员列表的逻辑
        return R.ok(new ArrayList<>());
    }

    /**
     * 构建设备工时记录
     */
    private List<ErpTaskReportEquipment> buildEquipmentRecords(String reportId, List<ErpTaskReportEquipment> equipmentList) {
        if (CollUtil.isEmpty(equipmentList)) {
            return new ArrayList<>();
        }

        List<ErpTaskReportEquipment> records = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (ErpTaskReportEquipment equipment : equipmentList) {
            ErpTaskReportEquipment record = new ErpTaskReportEquipment();
            record.setId(SequenceUtils.nextIdStr());
            record.setReportId(reportId);
            record.setEquipmentId(equipment.getEquipmentId());
            record.setEquipmentCode(equipment.getEquipmentCode());
            record.setEquipmentName(equipment.getEquipmentName());
            record.setWorkHours(equipment.getWorkHours());
            record.setMoldId(equipment.getMoldId());
            record.setRemark(equipment.getRemark());
            record.setCreateTime(now);
            records.add(record);
        }

        return records;
    }

    /**
     * 构建人员工时记录
     */
    private List<ErpTaskReportStaff> buildStaffRecords(String reportId, List<ErpTaskReportStaff> staffList) {
        if (CollUtil.isEmpty(staffList)) {
            return new ArrayList<>();
        }

        List<ErpTaskReportStaff> records = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (ErpTaskReportStaff staff : staffList) {
            ErpTaskReportStaff record = new ErpTaskReportStaff();
            record.setId(SequenceUtils.nextIdStr());
            record.setReportId(reportId);
            record.setStaffId(staff.getStaffId());
            record.setStaffName(staff.getStaffName());
            record.setPosition(staff.getPosition());
            record.setWorkContent(staff.getWorkContent());
            record.setWorkHours(staff.getWorkHours());
            record.setRemark(staff.getRemark());
            record.setCreateTime(now);
            records.add(record);
        }

        return records;
    }

    /**
     * 校验报工时间冲突
     * 在一个时间区间内一个工作中心只能有一条报工数据
     */
    private boolean validateTaskReportTimeConflict(LocalDateTime startTime, LocalDateTime endTime, String workCenterId) {
        if (startTime == null || endTime == null || StrUtil.isBlank(workCenterId)) {
            return false;
        }

        // 检查时间区间是否合理
        if (startTime.isAfter(endTime)) {
            return false;
        }

        // 查询该工作中心在时间区间内是否已有报工记录
        // 检查是否存在时间重叠的情况：
        // 1. 新记录的开始时间在已有记录的时间区间内
        // 2. 新记录的结束时间在已有记录的时间区间内
        // 3. 新记录的时间区间完全包含已有记录的时间区间
        LambdaQueryWrapper<ErpTaskReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ErpTaskReport::getWorkCenterId, workCenterId)
                   .and(wrapper -> wrapper
                       .and(innerWrapper -> innerWrapper
                           .le(ErpTaskReport::getStartTime, startTime)
                           .ge(ErpTaskReport::getEndTime, startTime)
                       )
                       .or(innerWrapper -> innerWrapper
                           .le(ErpTaskReport::getStartTime, endTime)
                           .ge(ErpTaskReport::getEndTime, endTime)
                       )
                       .or(innerWrapper -> innerWrapper
                           .ge(ErpTaskReport::getStartTime, startTime)
                           .le(ErpTaskReport::getEndTime, endTime)
                       )
                   );

        long count = taskReportMapper.selectCount(queryWrapper);
        return count == 0;
    }

    /**
     * 判断是否为返工工序任务
     */
    private boolean isReworkProcessTask(ErpProcessTask task) {
        // 检查工序任务是否为返工工序
        // 通过 is_rework_process 字段来判断
        return task.getIsReworkProcess() != null && task.getIsReworkProcess() == 1;
    }

    @Override
    public R<List<ProductionDailyReportVO>> queryProductionDailyReport(
            String startDateStr,
            String endDateStr,
            String workCenterId,
            String operatorName) {
        try {
            // 字符串转 LocalDate，如果为空则使用默认值
            LocalDate startDate = parseDate(startDateStr, LocalDate.now().minusDays(30));
            LocalDate endDate = parseDate(endDateStr, LocalDate.now());

            List<ProductionDailyReportVO> result = taskReportMapper.queryProductionDailyReport(
                    startDate, endDate, workCenterId, operatorName);

            return R.ok(result);
        } catch (Exception e) {
            log.error("查询生产日报详细数据失败", e);
            return R.failed("查询生产日报详细数据失败: " + e.getMessage());
        }
    }

    @Override
    public R<List<ProductionDailySummaryVO>> queryProductionDailySummary(
            String startDateStr,
            String endDateStr) {
        try {
            // 字符串转 LocalDate，如果为空则使用默认值
            LocalDate startDate = parseDate(startDateStr, LocalDate.now().minusDays(30));
            LocalDate endDate = parseDate(endDateStr, LocalDate.now());

            List<ProductionDailySummaryVO> result = taskReportMapper.queryProductionDailySummary(
                    startDate, endDate);

            return R.ok(result);
        } catch (Exception e) {
            log.error("查询生产日报汇总数据失败", e);
            return R.failed("查询生产日报汇总数据失败: " + e.getMessage());
        }
    }

    @Override
    public R<List<ProductionPlanExecutionVO>> queryProductionPlanExecution(
            String startDateStr,
            String endDateStr,
            String workCenterId,
            String planStatus) {
        try {
            // 字符串转 LocalDate，如果为空则使用默认值
            LocalDate startDate = parseDate(startDateStr, LocalDate.now().minusDays(30));
            LocalDate endDate = parseDate(endDateStr, LocalDate.now());

            List<ProductionPlanExecutionVO> result = taskReportMapper.queryProductionPlanExecution(
                    startDate, endDate, workCenterId, planStatus);

            return R.ok(result);
        } catch (Exception e) {
            log.error("查询生产计划执行情况明细失败", e);
            return R.failed("查询生产计划执行情况明细失败: " + e.getMessage());
        }
    }

    @Override
    public R<List<ProductionPlanExecutionSummaryVO>> queryProductionPlanExecutionSummary(
            String startDateStr,
            String endDateStr) {
        try {
            // 字符串转 LocalDate，如果为空则使用默认值
            LocalDate startDate = parseDate(startDateStr, LocalDate.now().minusDays(30));
            LocalDate endDate = parseDate(endDateStr, LocalDate.now());

            List<ProductionPlanExecutionSummaryVO> result = taskReportMapper.queryProductionPlanExecutionSummary(
                    startDate, endDate);

            return R.ok(result);
        } catch (Exception e) {
            log.error("查询生产计划执行情况汇总失败", e);
            return R.failed("查询生产计划执行情况汇总失败: " + e.getMessage());
        }
    }

    /**
     * 解析日期字符串为 LocalDate
     * @param dateStr 日期字符串，格式：yyyy-MM-dd
     * @param defaultValue 默认值
     * @return LocalDate
     */
    private LocalDate parseDate(String dateStr, LocalDate defaultValue) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            log.warn("日期格式解析失败，使用默认值。输入：{}，错误：{}", dateStr, e.getMessage());
            return defaultValue;
        }
    }

    @Override
    public void exportProductionDailyReport(String startDateStr, String endDateStr,
                                           String workCenterId, String operatorName,
                                           String format, HttpServletResponse response) {
        try {
            log.info("开始使用SimpleReportService导出生产日报，格式：{}，日期范围：{} - {}", format, startDateStr, endDateStr);

            // 检查SimpleReportService是否可用
            if (simpleReportService == null) {
                log.warn("SimpleReportService未配置，导出失败");
                throw new RuntimeException("报表服务未配置");
            }

            // 字符串转 LocalDate
            LocalDate startDate = parseDate(startDateStr, LocalDate.now().minusDays(30));
            LocalDate endDate = parseDate(endDateStr, LocalDate.now());

            // 获取生产日报数据
            List<ProductionDailyReportVO> reportData = taskReportMapper.queryProductionDailyReport(
                    startDate, endDate, workCenterId, operatorName);

            log.info("获取到{}条生产日报数据", reportData.size());

            // 🎯 使用终极简洁API - ReportInfo统一入参，避免选择困难症
            // 🔑 使用本地协议，通过文件修改时间智能判断是否需要清除缓存
            String templatePath = "local:production_daily_report_export.ureport.xml";
            String reportFormat = "pdf".equalsIgnoreCase(format) ? ReportInfoBuilder.FORMAT_PDF : ReportInfoBuilder.FORMAT_EXCEL;

            ReportInfo reportInfo = ReportInfoBuilder.create(templatePath)
                    .format(reportFormat)
                    .dataset("生产日报数据", reportData)  // 直接传VO对象
                    .param("startDate", startDate.toString())
                    .param("endDate", endDate.toString())
                    .param("workCenterId", workCenterId != null ? workCenterId : "全部")
                    .param("operatorName", operatorName != null ? operatorName : "全部")
                    .param("exportTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .param("totalRecords", reportData.size())
                    .param("exportUser", SecurityUtils.getUser().getUsername())
                    .build();

            simpleReportService.export(reportInfo, response); // Unified API call

            log.info("使用SimpleReportService导出生产日报成功，共导出{}条记录", reportData.size());

        } catch (Exception e) {
            log.error("导出生产日报失败", e);
            throw new RuntimeException("导出生产日报失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void printProductionDailyReport(String startDateStr, String endDateStr,
                                         String workCenterId, String operatorName,
                                         HttpServletResponse response) {
        try {
            log.info("开始使用SimpleReportService生成生产日报打印版本，日期范围：{} - {}", startDateStr, endDateStr);

            // 检查SimpleReportService是否可用
            if (simpleReportService == null) {
                log.warn("SimpleReportService未配置，打印失败");
                throw new RuntimeException("报表服务未配置");
            }

            // 字符串转 LocalDate
            LocalDate startDate = parseDate(startDateStr, LocalDate.now().minusDays(30));
            LocalDate endDate = parseDate(endDateStr, LocalDate.now());

            // 获取生产日报数据
            List<ProductionDailyReportVO> reportData = taskReportMapper.queryProductionDailyReport(
                    startDate, endDate, workCenterId, operatorName);

            log.info("获取到{}条生产日报数据", reportData.size());

            // 🎯 使用终极简洁API - ReportInfo统一入参，避免选择困难症
            // 🔑 使用本地协议，打印版本使用专门的模板
            String templatePath = "local:production_daily_report_print.ureport.xml";

            ReportInfo reportInfo = ReportInfoBuilder.create(templatePath)
                    .format(ReportInfoBuilder.FORMAT_PDF)  // 打印版本使用PDF格式
                    .dataset("生产日报数据", reportData)  // 直接传VO对象
                    .param("startDate", startDate.toString())
                    .param("endDate", endDate.toString())
                    .param("workCenterId", workCenterId != null ? workCenterId : "全部")
                    .param("operatorName", operatorName != null ? operatorName : "全部")
                    .param("printTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .param("totalRecords", reportData.size())
                    .param("printUser", SecurityUtils.getUser().getUsername())
                    .build();

            simpleReportService.export(reportInfo, response); // Unified API call

            log.info("使用SimpleReportService生成生产日报打印版本成功，共{}条记录", reportData.size());

        } catch (Exception e) {
            log.error("生成生产日报打印版本失败", e);
            throw new RuntimeException("生成生产日报打印版本失败: " + e.getMessage(), e);
        }
    }
}