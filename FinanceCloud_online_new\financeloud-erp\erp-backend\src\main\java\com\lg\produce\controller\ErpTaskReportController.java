package com.lg.produce.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lg.financecloud.common.core.util.R;
import com.lg.produce.entity.ErpTaskReport;
import com.lg.produce.service.ErpTaskReportService;
import com.lg.produce.vo.ErpTaskReportEquipmentVO;
import com.lg.produce.vo.ErpTaskReportStaffVO;
import com.lg.produce.vo.ErpTaskReportVO;
import com.lg.produce.vo.ProductionDailyReportVO;
import com.lg.produce.vo.ProductionDailySummaryVO;
import com.lg.produce.vo.ProductionPlanExecutionVO;
import com.lg.produce.vo.ProductionPlanExecutionSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * 工序任务报工 控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/production/work-reports")
@Api(value = "ErpTaskReportController", tags = "工序任务报工接口")
public class ErpTaskReportController {

    private final ErpTaskReportService taskReportService;

    /**
     * 分页查询报工记录
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询报工记录", notes = "分页查询报工记录")
    public R<IPage<ErpTaskReportVO>> pageList(
            @ApiParam(value = "页码", required = true) @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页条数", required = true) @RequestParam(defaultValue = "20") Integer pageSize,
            ErpTaskReportVO queryVO) {
        
        Page<ErpTaskReport> page = new Page<>(pageNum, pageSize);
        IPage<ErpTaskReportVO> result = taskReportService.pageList(page, queryVO);
        return R.ok(result);
    }

    /**
     * 查询报工详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "查询报工详情", notes = "查询报工详情")
    public R<ErpTaskReportVO> getDetail(
            @ApiParam(value = "报工ID", required = true) @PathVariable String id) {
        ErpTaskReportVO result = taskReportService.getDetailById(id);
        return result != null ? R.ok(result) : R.failed("未找到该报工记录");
    }

    /**
     * 创建报工记录
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建报工记录", notes = "创建报工记录")
    public R<ErpTaskReportVO> create(
            @RequestBody ErpTaskReportVO reportVO) {
        return taskReportService.createTaskReport(reportVO);
    }

    /**
     * 更新报工记录
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新报工记录", notes = "更新报工记录")
    public R<ErpTaskReportVO> update(
            @ApiParam(value = "报工ID", required = true) @PathVariable String id,
            @RequestBody ErpTaskReportVO reportVO) {
        
        reportVO.setId(id);
        return taskReportService.updateTaskReport(reportVO);
    }

    /**
     * 删除报工记录
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除报工记录", notes = "删除报工记录")
    public R<Boolean> delete(
            @ApiParam(value = "报工ID", required = true) @PathVariable String id) {
        return taskReportService.deleteTaskReport(id);
    }

    /**
     * 根据工单ID查询报工记录列表
     */
    @GetMapping("/by-work-order/{workOrderId}")
    @ApiOperation(value = "根据工单ID查询报工记录列表", notes = "根据工单ID查询报工记录列表")
    public R<List<ErpTaskReportVO>> getByWorkOrderId(
            @ApiParam(value = "工单ID", required = true) @PathVariable String workOrderId) {
        List<ErpTaskReportVO> result = taskReportService.getReportsByWorkOrderId(workOrderId);
        return R.ok(result);
    }

    /**
     * 根据任务ID查询报工记录列表
     */
    @GetMapping("/by-task/{taskId}")
    @ApiOperation(value = "根据任务ID查询报工记录列表", notes = "根据任务ID查询报工记录列表")
    public R<List<ErpTaskReportVO>> getByTaskId(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        List<ErpTaskReportVO> result = taskReportService.getReportsByTaskId(taskId);
        return R.ok(result);
    }
    
    /**
     * 获取工作中心的设备列表
     */
    @GetMapping("/work-centers/{workCenterId}/equipment")
    @ApiOperation(value = "获取工作中心的设备列表", notes = "获取工作中心的设备列表")
    public R<List<ErpTaskReportEquipmentVO>> getWorkCenterEquipments(
            @ApiParam(value = "工作中心ID", required = true) @PathVariable String workCenterId) {
        return taskReportService.getWorkCenterEquipments(workCenterId);
    }
    
    /**
     * 获取工作中心的人员列表
     */
    @GetMapping("/work-centers/{workCenterId}/staff")
    @ApiOperation(value = "获取工作中心的人员列表", notes = "获取工作中心的人员列表")
    public R<List<ErpTaskReportStaffVO>> getWorkCenterStaff(
            @ApiParam(value = "工作中心ID", required = true) @PathVariable String workCenterId) {
        return taskReportService.getWorkCenterStaff(workCenterId);
    }
    
    /**
     * 查询详细生产日报
     */
    @GetMapping("/daily-report")
    @ApiOperation(value = "查询详细生产日报", notes = "查询详细生产日报")
    public R<List<ProductionDailyReportVO>> queryProductionDailyReport(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate,
            @ApiParam(value = "工作中心ID") @RequestParam(required = false) String workCenterId,
            @ApiParam(value = "操作员姓名") @RequestParam(required = false) String operatorName) {
        return taskReportService.queryProductionDailyReport(startDate, endDate, workCenterId, operatorName);
    }
    
    /**
     * 查询生产日报汇总
     */
    @GetMapping("/daily-summary")
    @ApiOperation(value = "查询生产日报汇总", notes = "查询生产日报汇总")
    public R<List<ProductionDailySummaryVO>> queryProductionDailySummary(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate) {
        return taskReportService.queryProductionDailySummary(startDate, endDate);
    }
    
    /**
     * 查询生产计划执行情况明细
     */
    @GetMapping("/plan-execution")
    @ApiOperation(value = "查询生产计划执行情况明细", notes = "查询生产计划执行情况明细")
    public R<List<ProductionPlanExecutionVO>> queryProductionPlanExecution(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate,
            @ApiParam(value = "工作中心ID") @RequestParam(required = false) String workCenterId,
            @ApiParam(value = "计划状态") @RequestParam(required = false) String planStatus) {
        return taskReportService.queryProductionPlanExecution(startDate, endDate, workCenterId, planStatus);
    }
    
    /**
     * 查询生产计划执行情况汇总
     */
    @GetMapping("/plan-execution-summary")
    @ApiOperation(value = "查询生产计划执行情况汇总", notes = "查询生产计划执行情况汇总")
    public R<List<ProductionPlanExecutionSummaryVO>> queryProductionPlanExecutionSummary(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate) {
        return taskReportService.queryProductionPlanExecutionSummary(startDate, endDate);
    }

    /**
     * 导出生产日报
     */
    @GetMapping("/export")
    @ApiOperation(value = "导出生产日报", notes = "使用UReport导出生产日报")
    public void exportProductionDailyReport(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate,
            @ApiParam(value = "工作中心ID") @RequestParam(required = false) String workCenterId,
            @ApiParam(value = "操作员姓名") @RequestParam(required = false) String operatorName,
            @ApiParam(value = "导出格式：excel/pdf") @RequestParam(defaultValue = "excel") String format,
            HttpServletResponse response) {
        taskReportService.exportProductionDailyReport(startDate, endDate, workCenterId, operatorName, format, response);
    }

    /**
     * 打印生产日报
     */
    @GetMapping("/print")
    @ApiOperation(value = "打印生产日报", notes = "使用UReport打印生产日报")
    public void printProductionDailyReport(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate,
            @ApiParam(value = "工作中心ID") @RequestParam(required = false) String workCenterId,
            @ApiParam(value = "操作员姓名") @RequestParam(required = false) String operatorName,
            HttpServletResponse response) {
        taskReportService.printProductionDailyReport(startDate, endDate, workCenterId, operatorName, response);
    }
}