package com.lg.financecloud.common.report.provider;

import cn.hutool.core.io.IoUtil;
import com.bstek.ureport.provider.report.ReportProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简化的本地报表提供者
 * 支持从resources目录读取报表模板
 *
 * <AUTHOR> UReport Team
 * @since 2025-08-23
 */
@Slf4j
public class EnhancedLocalReportProvider implements ReportProvider {

    private static final String LOCAL_PREFIX = "local:";

    // 🔑 参考SqlTemplateManager的做法：预定义搜索路径
    private static final String[] TEMPLATE_LOCATIONS = {
        "classpath*:reports/**/*.ureport.xml"
//        "classpath*:template/**/*.ureport.xml",
//        "classpath*:report-templates/**/*.ureport.xml"
    };

    private final PathMatchingResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
    private final Map<String, String> templateCache = new ConcurrentHashMap<>();

    // 🔑 构造函数中初始化加载所有模板
    public EnhancedLocalReportProvider() {
        loadAllTemplates();
    }
    
    @Override
    public InputStream loadReport(String file) {
        // 🔑 只处理带协议前缀的情况：local:templateName
        if (!file.startsWith(LOCAL_PREFIX)) {
            return null;
        }

        String templateCode = file.substring(LOCAL_PREFIX.length());
        log.debug("加载本地报表模板: {}", templateCode);

        try {
            String content = getTemplateContent(templateCode);
            if (content != null) {
                log.info("✅ 成功加载本地报表模板: {}", templateCode);
                return new ByteArrayInputStream(content.getBytes("UTF-8"));
            } else {
                log.warn("❌ 本地报表模板不存在: {}", templateCode);
                return null;
            }
        } catch (Exception e) {
            log.error("❌ 加载本地报表模板失败: {}", templateCode, e);
            return null;
        }
    }

    /**
     * 获取模板内容
     */
    /**
     * 🔑 智能获取模板内容：兼容带后缀和不带后缀的情况
     */
    private String getTemplateContent(String templateCode) {
        log.debug("🔍 获取本地模板: {}", templateCode);

        // 🔑 方式1：直接匹配（优先）
        String content = templateCache.get(templateCode);
        if (content != null) {
            log.debug("✅ 直接匹配获取模板: {}", templateCode);
            return content;
        }

        // 🔑 方式2：如果传入的是不带后缀的，尝试添加后缀匹配
        if (!templateCode.contains(".")) {
            String[] suffixes = {".ureport.xml", ".ureport"};
            for (String suffix : suffixes) {
                String fullName = templateCode + suffix;
                content = templateCache.get(fullName);
                if (content != null) {
                    log.debug("✅ 添加后缀匹配获取模板: {} -> {}", templateCode, fullName);
                    return content;
                }
            }
        }

        // 🔑 方式3：如果传入的是带后缀的，尝试去掉后缀匹配
        if (templateCode.contains(".")) {
            String baseName = templateCode.replaceAll("\\.(ureport\\.xml|ureport)$", "");
            content = templateCache.get(baseName);
            if (content != null) {
                log.debug("✅ 去掉后缀匹配获取模板: {} -> {}", templateCode, baseName);
                return content;
            }
        }

        // 🔑 方式4：模糊匹配（兜底）
        for (String cachedKey : templateCache.keySet()) {
            String cachedBaseName = cachedKey.replaceAll("\\.(ureport\\.xml|ureport)$", "");
            String inputBaseName = templateCode.replaceAll("\\.(ureport\\.xml|ureport)$", "");

            if (cachedBaseName.equals(inputBaseName)) {
                content = templateCache.get(cachedKey);
                log.debug("✅ 模糊匹配获取模板: {} -> {}", templateCode, cachedKey);
                return content;
            }
        }

        log.debug("❌ 模板不存在: {} (缓存中共有 {} 个模板: {})",
                 templateCode, templateCache.size(), templateCache.keySet());
        return null;
    }
    
    @Override
    public void deleteReport(String file) {
        log.warn("本地报表模板不支持删除操作: {}", file);
        throw new UnsupportedOperationException("本地报表模板不支持删除操作");
    }
    
    @Override
    public List<com.bstek.ureport.provider.report.ReportFile> getReportFiles() {
        List<com.bstek.ureport.provider.report.ReportFile> files = new ArrayList<>();

        try {
            // 🔑 使用新的搜索路径数组
            for (String location : TEMPLATE_LOCATIONS) {
                Resource[] resources = resourceResolver.getResources(location);

                for (Resource resource : resources) {
                    String filename = resource.getFilename();
                    if (filename != null && filename.endsWith(".ureport.xml")) {
                        String templateCode = filename.substring(0, filename.length() - 12);
                        files.add(new com.bstek.ureport.provider.report.ReportFile(
                                templateCode,
                                new Date()
                        ));
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取本地报表文件列表失败", e);
        }

        return files;
    }
    
    @Override
    public void saveReport(String file, String content) {
        log.warn("本地报表模板不支持保存操作: {}", file);
        throw new UnsupportedOperationException("本地报表模板不支持保存操作，请直接修改resources目录下的文件");
    }
    
    @Override
    public String getName() {
        return "本地文件存储";
    }
    
    @Override
    public boolean disabled() {
        return false;
    }
    
    @Override
    public String getPrefix() {
        return LOCAL_PREFIX;
    }
    
    /**
     * 检查模板是否存在
     * @param templateCode 模板代码
     * @return 是否存在
     */
    public boolean hasTemplate(String templateCode) {
        return getTemplateContent(templateCode) != null;
    }

    /**
     * 🔑 参考SqlTemplateManager：批量加载所有模板
     */
    private void loadAllTemplates() {
        log.info("🔄 开始批量加载本地模板...");
        int totalLoaded = 0;

        try {
            for (String location : TEMPLATE_LOCATIONS) {
                log.debug("📂 搜索路径: {}", location);
                Resource[] resources = resourceResolver.getResources(location);
                log.info("📋 路径 {} 找到 {} 个模板文件", location, resources.length);

                for (Resource resource : resources) {
                    try {
                        loadSingleResource(resource);
                        totalLoaded++;
                    } catch (Exception e) {
                        log.warn("❌ 加载模板失败: {} - {}", resource.getFilename(), e.getMessage());
                    }
                }
            }

            log.info("✅ 批量加载完成，共加载 {} 个本地模板", totalLoaded);
            if (totalLoaded > 0) {
                log.info("📋 已加载的模板: {}", String.join(", ", templateCache.keySet()));
            }

        } catch (Exception e) {
            log.error("❌ 批量加载本地模板失败", e);
        }
    }

    /**
     * 🔑 参考SqlTemplateManager：加载单个资源文件
     */
    private void loadSingleResource(Resource resource) throws Exception {
        try (InputStream inputStream = resource.getInputStream()) {
            String content = IoUtil.read(inputStream, StandardCharsets.UTF_8);
            String filename = resource.getFilename();

            if (filename != null) {
                // 提取模板代码（不需要去掉.ureport.xml后缀）兼容　不带后缀
                String templateCode = filename;
                templateCache.put(templateCode, content);
                log.debug("✅ 加载模板: {} -> {}", templateCode, resource.getURI());
            }
        }
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        templateCache.clear();
        log.info("清空本地模板缓存");
        // 重新加载
        loadAllTemplates();
    }
    

    

}
