# ============================================
# Cursor IDE 忽略索引配置文件
# 此文件控制 Cursor AI 功能的文件索引范围
# ============================================

# ============================================
# 后端Java项目忽略配置
# ============================================

# 忽略整个微服务模块（非MRP相关）
FinanceCloud_online_new/financecloud-register/
FinanceCloud_online_new/financecloud-visual/
#FinanceCloud_online_new/financecloud-gateway/
#FinanceCloud_online_new/financecloud-flow/
#FinanceCloud_online_new/financecloud-oa/
#FinanceCloud_online_new/financecloud-finance/
#FinanceCloud_online_new/financecloud-upms/
#FinanceCloud_online_new/financecloud-common/
FinanceCloud_online_new/augment_vip/

# 忽略非MRP相关的业务模块
FinanceCloud_online_new/financeloud-erp/erp-common/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/adminassistant/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/checkwork/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/crm/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/ehr/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/outsourcing/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/ifs/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/wagas/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/engineering/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/schedule/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/userauth/

# 忽略非MRP相关的Mapper文件
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/adminassistant/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/checkwork/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/crm/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/ehr/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/wages/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/userauth/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/schedule/
#FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/produce/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/outsourcing/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/mapper/ifs/

# 忽略配置和通用代码（非业务核心）
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/aop/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/api/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/common/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/config/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/db/config/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/java/com/lg/start/
FinanceCloud_online_new/financeloud-erp/erp-backend/src/main/resources/reqmapping/

# ============================================
# 前端Vue项目忽略配置
# ============================================

# Node.js相关
FinanceCloud-plt/node_modules/
FinanceCloud-plt/dist/
FinanceCloud-plt/scripts/

# 测试文件
FinanceCloud-plt/tests/

# 静态资源（大文件）
FinanceCloud-plt/public/
FinanceCloud-plt/assets/
FinanceCloud-plt/admin/
FinanceCloud-plt/administration/
FinanceCloud-plt/baseNotice/
FinanceCloud-plt/ehr/
FinanceCloud-plt/finance/
FinanceCloud-plt/financial/
FinanceCloud-plt/oa/
FinanceCloud-plt/outsourcing/
FinanceCloud-plt/processManager/
FinanceCloud-plt/wagas/
FinanceCloud-plt/wages/
FinanceCloud-plt/workFlow/
FinanceCloud-plt/locales/
FinanceCloud-plt/styles/

# 非MRP相关的前端页面
FinanceCloud-plt/src/views/administration/
#FinanceCloud-plt/src/views/admin/
FinanceCloud-plt/src/views/baseNotice/
FinanceCloud-plt/src/views/ehr/
FinanceCloud-plt/src/views/projectManager/
FinanceCloud-plt/src/views/task/
FinanceCloud-plt/src/views/wages/
FinanceCloud-plt/src/views/workflow/
FinanceCloud-plt/src/views/outsourcing/
FinanceCloud-plt/src/views/process-management/
FinanceCloud-plt/src/views/financial/
#FinanceCloud-plt/src/page/
FinanceCloud-plt/src/oa/
FinanceCloud-plt/src/styles/

# ============================================
# 通用忽略配置
# ============================================

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 环境配置文件
.env
.env.local
.env.*.local
.env.development
.env.production

# IDE配置文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj*
*.sln
*.sw*
*.swp
*.swo

# 依赖锁文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 构建产物
target/
build/
dist/
out/
.next/
.nuxt/

# 临时文件
*.tmp
*.temp
*.cache
.cache/
.parcel-cache/

# 备份文件
*.bak
*.backup
*.orig

# 大型二进制文件
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
*.7z

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 文档生成目录
docs/build/
site/
coverage/

# Maven/Gradle构建目录
.gradle/
gradle/
gradlew
gradlew.bat

# Java编译文件
*.class
*.jar
*.war
*.ear

# Spring Boot
spring.log
application-*.properties
application-*.yml

# 前端工具链
.eslintcache
.stylelintcache
.sass-cache/
.postcss-cache/

# 测试覆盖率
coverage/
nyc_output/
.coverage/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db