<template>
    <div class="conten_body_main margintop10">
        <top-search @query="fun_getTableListData" @reset="fun_searchReset" ref="topSearch">
            <template #toolBar>
                <vxe-button :content="$t('刷新')" @click="fun_getTableListData"></vxe-button>
                <vxe-button :content="$t('导出')" icon="el-icon-download" @click="fun_exportExcel"></vxe-button>
                <vxe-button :content="$t('打印')" icon="el-icon-printer" @click="fun_printReport"></vxe-button>
                <vxe-toolbar ref="xToolbar" :custom="{icon:'vxe-button--icon vxe-icon-custom-column'}"></vxe-toolbar>
            </template>
        </top-search>
        
        <el-container>
            <el-main>
                <!-- 统计概览 -->
                <el-row :gutter="20" class="overview-cards" v-if="overviewData">
                    <el-col :span="6">
                        <el-card class="overview-card work-center-card">
                            <div class="card-icon">
                                <i class="el-icon-s-data"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-title">{{ $t('总工作中心数') }}</div>
                                <div class="card-value">{{ overviewData.totalWorkCenters || 0 }}</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card class="overview-card operator-card">
                            <div class="card-icon">
                                <i class="el-icon-user"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-title">{{ $t('总操作员数') }}</div>
                                <div class="card-value">{{ overviewData.totalOperators || 0 }}</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card class="overview-card equipment-card">
                            <div class="card-icon">
                                <i class="el-icon-s-tools"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-title">{{ $t('总设备数') }}</div>
                                <div class="card-value">{{ overviewData.totalEquipments || 0 }}</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card class="overview-card task-card">
                            <div class="card-icon">
                                <i class="el-icon-s-order"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-title">{{ $t('总任务数') }}</div>
                                <div class="card-value">{{ overviewData.totalTasks || 0 }}</div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 汇总数据表格 -->
                <vxe-table 
                    :loading="loading" 
                    class="mytable-scrollbar" 
                    show-overflow="tooltip" 
                    size="mini" 
                    ref="ListTableRef"  
                    border 
                    stripe 
                    header-align="left"
                    :height="contentStyleObj.height" 
                    :data="tableList" 
                    row-id="reportDate"
                    id="productionDailySummaryTableList" 
                    resizable 
                    :custom-config="{storage: true}"
                    :filter-config="{remote:true}" 
                    @filter-change="handleFilterChange"
                    :sort-config="{remote:true}" 
                    @sort-change="handeleSortChange">
                    
                    <vxe-column field="reportDate" :title="$t('报表日期')" width="120" :params="{searchType:'date'}" sortable fixed="left">
                        <template #default="{row}">{{ formatDate(row.reportDate) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="workCenterCount" :title="$t('工作中心数')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-primary">{{ row.workCenterCount }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="operatorCount" :title="$t('操作员数')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.operatorCount }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="equipmentCount" :title="$t('设备数')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-warning">{{ row.equipmentCount || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalTasks" :title="$t('总任务数')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-info">{{ row.totalTasks }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalCompletedQty" :title="$t('总完成数量')" width="130" sortable>
                        <template #default="{row}">
                            <span class="text-primary font-bold">{{ row.totalCompletedQty }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalQualifiedQty" :title="$t('总合格数量')" width="130" sortable>
                        <template #default="{row}">
                            <span class="text-success font-bold">{{ row.totalQualifiedQty }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalDefectiveQty" :title="$t('总不良数量')" width="130" sortable>
                        <template #default="{row}">
                            <span class="text-danger font-bold">{{ row.totalDefectiveQty }}</span>
                        </template>
                    </vxe-column>
                    
                                         <vxe-column field="overallQualifiedRate" :title="$t('整体合格率')" width="130" sortable visible="false">
                         <template #default="{row}">
                             <span :class="getQualifiedRateClass(row.overallQualifiedRate)" class="font-bold">
                                 {{ row.overallQualifiedRate }}%
                             </span>
                         </template>
                     </vxe-column>
                     
                     <vxe-column field="overallDefectiveRate" :title="$t('整体不良率')" width="130" sortable visible="false">
                         <template #default="{row}">
                             <span :class="getDefectiveRateClass(row.overallDefectiveRate)" class="font-bold">
                                 {{ row.overallDefectiveRate }}%
                             </span>
                         </template>
                     </vxe-column>
                    
                    <vxe-column field="totalExceptions" :title="$t('总异常次数')" width="130" sortable>
                        <template #default="{row}">
                            <span v-if="row.totalExceptions > 0" class="text-warning font-bold">
                                {{ row.totalExceptions }}
                            </span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalDowntimeEvents" :title="$t('总停机事件')" width="130" sortable>
                        <template #default="{row}">
                            <span v-if="row.totalDowntimeEvents > 0" class="text-danger font-bold">
                                {{ row.totalDowntimeEvents }}
                            </span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalDowntimeDuration" :title="$t('总停机时长(小时)')" width="150" sortable>
                        <template #default="{row}">
                            <span v-if="row.totalDowntimeDuration > 0" class="text-danger font-bold">
                                {{ row.totalDowntimeDuration }}
                            </span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="avgTaskDurationMinutes" :title="$t('平均任务时长(分钟)')" width="150" sortable>
                        <template #default="{row}">
                            <span class="text-info">{{ Math.round(row.avgTaskDurationMinutes) }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalEquipmentHours" :title="$t('总设备工时')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-info">{{ row.totalEquipmentHours || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalStaffHours" :title="$t('总人员工时')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.totalStaffHours || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="avgOutputPerWorkCenter" :title="$t('平均工作中心产出')" width="160" sortable>
                        <template #default="{row}">
                            <span class="text-primary font-bold">{{ row.avgOutputPerWorkCenter }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="avgOutputPerEquipment" :title="$t('平均设备产出')" width="140" sortable>
                        <template #default="{row}">
                            <span class="text-warning font-bold">{{ row.avgOutputPerEquipment || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="op" fixed="right" width="120" :title="$t('操作')">
                        <template #default="{ row }">
                            <div class="text-butons-group">
                                <el-button @click.stop="fun_viewDetail(row)" type="text" icon="el-icon-view">
                                    {{ $t('查看详情') }}
                                </el-button>
                            </div>
                        </template>
                    </vxe-column>
                </vxe-table>
                
                <vxe-pager
                    align="center"
                    size="mini"
                    :current-page.sync="page.current"
                    :page-size.sync="page.size"
                    :total="page.total"
                    perfect
                    background
                    :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                    @page-change="handlePageChange">
                </vxe-pager>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import TopSearch from "@/components/filterate-search/index";
import { getTableListData, exportExcel } from "@/api/erp/productionDailyReport";

export default {
    name: "ProductionDailySummary",
    components: {
        TopSearch
    },
    data() {
        return {
            loading: false,
            tableList: [],
            overviewData: null,
            searchFormData: {},
            page: {
                current: 1,
                size: 20,
                total: 0
            },
            contentStyleObj: {
                height: '600px'
            }
        };
    },
    mounted() {
        this.fun_getTableListData();
    },
    methods: {
        // 获取表格数据
        fun_getTableListData(filterParam) {
            this.loading = true;
            
            let pages = {
                page: this.page.current,
                limit: this.page.size
            };
            
            let params = Object.assign(pages, this.searchFormData, filterParam, { toUnderLineCase: true });
            Object.assign(this.searchFormData, {}, filterParam);
            
            getTableListData(params, 'summary').then(res => {
                const payload = res && res.data ? res.data : {};
                if (payload && (payload.code === 0 || payload.returnCode === '0')) {
                    const rows = Array.isArray(payload.data)
                        ? payload.data
                        : (payload.records || payload.rows || []);
                    this.tableList = rows;
                    this.page.total = payload.total || rows.length || 0;
                } else if (res.status === 200) {
                    this.tableList = payload.records || payload.rows || payload.data || [];
                    this.page.total = payload.total || this.tableList.length || 0;
                }
                this.calculateOverview();
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },
        
        // 计算概览数据
        calculateOverview() {
            if (this.tableList.length === 0) {
                this.overviewData = null;
                return;
            }
            
            const totalWorkCenters = this.tableList.reduce((sum, item) => sum + (item.workCenterCount || 0), 0);
            const totalOperators = this.tableList.reduce((sum, item) => sum + (item.operatorCount || 0), 0);
            const totalEquipments = this.tableList.reduce((sum, item) => sum + (item.equipmentCount || 0), 0);
            const totalTasks = this.tableList.reduce((sum, item) => sum + (item.totalTasks || 0), 0);
            
            this.overviewData = {
                totalWorkCenters,
                totalOperators,
                totalEquipments,
                totalTasks
            };
        },
        
        // 搜索重置
        fun_searchReset() {
            this.page.current = 1;
            this.fun_getTableListData();
        },
        
        // 分页变化
        handlePageChange({ currentPage, pageSize }) {
            this.page.current = currentPage;
            this.page.size = pageSize;
            this.fun_getTableListData();
        },
        
        // 排序变化
        handeleSortChange({ field, order }) {
            this.fun_getTableListData();
        },
        
        // 筛选变化
        handleFilterChange({ filters }) {
            this.fun_getTableListData();
        },
        
        // 查看详情
        fun_viewDetail(row) {
            // 跳转到详细页面，传递日期参数
            this.$router.push({
                name: 'ProductionDailyReportList',
                query: {
                    startDate: this.formatDate(row.reportDate),
                    endDate: this.formatDate(row.reportDate)
                }
            });
        },
        
        // 导出Excel
        fun_exportExcel() {
            console.log('开始导出Excel');
            const params = Object.assign({}, this.searchFormData, { toUnderLineCase: true });
            console.log('导出参数:', params);

            // 检查 commonExportFile 方法是否存在
            if (typeof this.commonExportFile === 'function') {
                console.log('调用 commonExportFile 方法');
                this.commonExportFile({
                    api: exportExcel,
                    params: params,
                    fileName: '生产日报汇总',
                    fileExt: 'xlsx',
                    fileNameNeedTimeSpan: true,
                    fileType: 'excel',
                    successMsg: '生产日报汇总导出成功',
                    loadingText: '正在导出生产日报汇总，请稍候...'
                });
            } else {
                console.error('commonExportFile 方法不存在');
                // 降级处理
                exportExcel(params).then(res => {
                    console.log('导出响应:', res);
                    this.$message.success('导出成功');
                }).catch(error => {
                    console.error('导出失败:', error);
                    this.$message.error('导出失败');
                });
            }
        },
        
        // 打印报表
        fun_printReport() {
            window.print();
        },
        
        // 格式化时间
        formatTime(timestr) {
            try {
                if (timestr) {
                    return timestr.substr(0, 10);
                } else {
                    return '';
                }
            } catch (e) {
                return '';
            }
        },
        
        // 新增：格式化日期（供模板与跳转使用）
        formatDate(dateStr) {
            return this.formatTime(dateStr);
        },
        
        // 获取合格率样式类
        getQualifiedRateClass(rate) {
            if (rate >= 95) return 'text-success';
            if (rate >= 85) return 'text-warning';
            return 'text-danger';
        },
        
        // 获取不良率样式类
        getDefectiveRateClass(rate) {
            if (rate <= 5) return 'text-success';
            if (rate <= 15) return 'text-warning';
            return 'text-danger';
        }
    }
};
</script>

<style scoped>
.overview-cards {
    margin-bottom: 30px;
}

.overview-card {
    height: 110px;
}

.overview-card .el-card__body {
    padding: 15px;
    height: 100%;
    display: flex;
    align-items: center;
}

.card-icon {
    font-size: 32px;
    margin-right: 15px;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 14px;
    color: #909399;
    margin-bottom: 8px;
}

.card-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
    line-height: 1.2;
}

.card-subtitle {
    font-size: 12px;
    color: #909399;
}

/* 不同类型卡片的主题色 */
.work-center-card .card-icon {
    color: #409EFF;
}

.operator-card .card-icon {
    color: #67C23A;
}

.equipment-card .card-icon {
    color: #F56C6C;
}

.task-card .card-icon {
    color: #E6A23C;
}

.text-primary {
    color: #409EFF;
}

.text-success {
    color: #67C23A;
}

.text-warning {
    color: #E6A23C;
}

.text-danger {
    color: #F56C6C;
}

.text-info {
    color: #909399;
}

.text-muted {
    color: #C0C4CC;
}

.font-bold {
    font-weight: bold;
}

.mytable-scrollbar {
    margin-top: 20px;
}

/* 表格样式调整 - 保持与现有功能一致 */
/deep/ .vxe-table .vxe-body--column {
    padding: 12px 8px;
    line-height: 1.4;
}

.text-butons-group {
    display: flex;
    gap: 5px;
}
</style>
