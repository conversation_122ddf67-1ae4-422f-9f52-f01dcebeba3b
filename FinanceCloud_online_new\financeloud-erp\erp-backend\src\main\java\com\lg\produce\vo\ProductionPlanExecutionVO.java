package com.lg.produce.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 生产计划执行情况报表VO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel(description = "生产计划执行情况报表")
public class ProductionPlanExecutionVO {

    @ApiModelProperty("计划日期")
    private LocalDate planDate;

    @ApiModelProperty("计划编号")
    private String planCode;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("工作中心ID")
    private Long workCenterId;

    @ApiModelProperty("工作中心编码")
    private String workCenterCode;

    @ApiModelProperty("工作中心名称")
    private String workCenterName;

    @ApiModelProperty("物料ID")
    private Long materialId;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料型号")
    private String materialModel;

    @ApiModelProperty("计划数量")
    private BigDecimal planQty;

    @ApiModelProperty("完成数量")
    private BigDecimal completedQty;

    @ApiModelProperty("合格数量")
    private BigDecimal qualifiedQty;

    @ApiModelProperty("不良数量")
    private BigDecimal defectiveQty;

    @ApiModelProperty("完成率")
    private BigDecimal completionRate;

    @ApiModelProperty("合格率")
    private BigDecimal qualifiedRate;

    @ApiModelProperty("计划开始时间")
    private LocalDateTime planStartTime;

    @ApiModelProperty("计划结束时间")
    private LocalDateTime planEndTime;

    @ApiModelProperty("实际开始时间")
    private LocalDateTime actualStartTime;

    @ApiModelProperty("实际结束时间")
    private LocalDateTime actualEndTime;

    @ApiModelProperty("计划状态")
    private String planStatus;

    @ApiModelProperty("计划状态名称")
    private String planStatusName;

    @ApiModelProperty("执行状态")
    private String executionStatus;

    @ApiModelProperty("执行状态名称")
    private String executionStatusName;

    @ApiModelProperty("延期天数")
    private Integer delayDays;

    @ApiModelProperty("任务数量")
    private Integer taskCount;

    @ApiModelProperty("已完成任务数")
    private Integer completedTaskCount;

    @ApiModelProperty("异常次数")
    private Integer exceptionCount;

    @ApiModelProperty("异常类型")
    private String exceptionTypes;

    @ApiModelProperty("负责人")
    private String responsiblePerson;

    @ApiModelProperty("备注")
    private String remarks;
}
