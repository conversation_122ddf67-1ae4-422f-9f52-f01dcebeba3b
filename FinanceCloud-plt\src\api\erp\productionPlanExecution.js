import request from '@/router/axios'

/**
 * 获取生产计划执行情况明细数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getTableListData(params) {
  return request({
    url: '/api/production/work-reports/plan-execution',
    method: 'get',
    params: params
  })
}

/**
 * 获取生产计划执行情况汇总数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getSummaryData(params) {
  return request({
    url: '/api/production/work-reports/plan-execution-summary',
    method: 'get',
    params: params
  })
}

/**
 * 导出生产计划执行情况Excel
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function exportExcel(params) {
  return request({
    url: '/api/production/work-reports/plan-execution/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
