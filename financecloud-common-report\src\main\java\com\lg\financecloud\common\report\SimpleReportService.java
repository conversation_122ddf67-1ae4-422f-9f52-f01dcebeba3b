package com.lg.financecloud.common.report;

import cn.hutool.core.util.IdUtil;
import com.lg.financecloud.common.report.dto.ReportInfo;
import com.lg.financecloud.common.report.provider.EnhancedLocalReportProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * 🎯 极简报表服务
 * 统一使用ReportInfo入参，避免选择困难症
 * 
 * 核心理念：
 * 1. 只有4个核心方法：export()、exportToStream()、previewHtml()、batchPrint()
 * 2. 统一使用ReportInfo封装所有参数
 * 3. 提供ReportInfo.builder()便捷构建
 *
 * <AUTHOR> Team  
 * @since 2025-01-27
 */
@Slf4j
@Service
public class SimpleReportService {

    @Autowired(required = false)
    @Qualifier("localReportProvider")
    private EnhancedLocalReportProvider localReportProvider;

    // ==================== 🎯 极简核心API（只有4个方法）====================

    /**
     * 🎯 导出到HTTP响应（核心方法1）
     * 统一入口，支持所有导出场景
     * 
     * @param reportInfo 报表信息（包含模板、数据、参数、格式等所有信息）
     * @param response HTTP响应
     */
    public void export(ReportInfo reportInfo, HttpServletResponse response) {
        try {
            log.info("开始导出报表: template={}, format={}, datasets={}", 
                    reportInfo.getReportCode(), reportInfo.getFormat(), 
                    reportInfo.getDs() != null ? reportInfo.getDs().size() : 0);

            // 构建模板路径
            String templatePath = resolveTemplatePath(reportInfo.getReportCode());
            
            // 设置响应头
            setResponseHeaders(response, reportInfo.getFormat(), reportInfo.getReportCode());
            
            // 准备参数
            Map<String, Object> parameters = prepareParameters(reportInfo);
            
            // 执行导出
            SimpleReportBuilder builder = SimpleReportBuilder.create(templatePath);
            builder.params(parameters);
            builder.exportToStream(reportInfo.getFormat(), response.getOutputStream());
            response.getOutputStream().flush();
            
            log.info("报表导出成功: template={}", reportInfo.getReportCode());

        } catch (Exception e) {
            log.error("报表导出失败: template={}", reportInfo.getReportCode(), e);
            throw new RuntimeException("报表导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🎯 导出为字节流（核心方法2）
     * 用于进一步处理或存储
     * 
     * @param reportInfo 报表信息
     * @return 字节流
     */
    public ByteArrayOutputStream exportToStream(ReportInfo reportInfo) {
        try {
            log.info("开始导出字节流: template={}, format={}", 
                    reportInfo.getReportCode(), reportInfo.getFormat());

            // 构建模板路径
            String templatePath = resolveTemplatePath(reportInfo.getReportCode());
            
            // 准备参数
            Map<String, Object> parameters = prepareParameters(reportInfo);
            
            // 执行导出
            SimpleReportBuilder builder = SimpleReportBuilder.create(templatePath);
            builder.params(parameters);
            ByteArrayOutputStream result = builder.exportToStream(reportInfo.getFormat());
            
            log.info("字节流导出成功: template={}", reportInfo.getReportCode());
            return result;

        } catch (Exception e) {
            log.error("字节流导出失败: template={}", reportInfo.getReportCode(), e);
            throw new RuntimeException("字节流导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🎯 预览HTML（核心方法3）
     * 用于报表预览
     * 
     * @param reportInfo 报表信息
     * @return HTML内容
     */
    public String previewHtml(ReportInfo reportInfo) {
        try {
            log.info("开始预览HTML: template={}", reportInfo.getReportCode());

            // 构建模板路径
            String templatePath = resolveTemplatePath(reportInfo.getReportCode());
            
            // 准备参数
            Map<String, Object> parameters = prepareParameters(reportInfo);
            
            // 执行预览
            SimpleReportBuilder builder = SimpleReportBuilder.create(templatePath);
            builder.params(parameters);
            String result = builder.exportToHtml();
            
            log.info("HTML预览成功: template={}", reportInfo.getReportCode());
            return result;

        } catch (Exception e) {
            log.error("HTML预览失败: template={}", reportInfo.getReportCode(), e);
            throw new RuntimeException("HTML预览失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🎯 批量打印（核心方法4）
     * 支持多个报表的批量打印，自动合并为一个文件
     * 
     * @param reportInfos 多个报表信息列表
     * @param response HTTP响应
     */
    public void batchPrint(List<ReportInfo> reportInfos, HttpServletResponse response) {
        batchPrint(reportInfos, "pdf", response);
    }

    /**
     * 🎯 批量打印 - 指定格式
     * 
     * @param reportInfos 多个报表信息列表
     * @param format 导出格式（推荐pdf）
     * @param response HTTP响应
     */
    public void batchPrint(List<ReportInfo> reportInfos, String format, HttpServletResponse response) {
        try {
            log.info("开始批量打印: 报表数量={}, format={}", reportInfos.size(), format);

            if (reportInfos == null || reportInfos.isEmpty()) {
                throw new IllegalArgumentException("报表列表不能为空");
            }

            // 设置响应头
            setResponseHeaders(response, format, "batch_report");
            
            // 执行批量打印
            if (reportInfos.size() == 1) {
                // 单个报表直接导出
                export(reportInfos.get(0), response);
            } else {
                // 多个报表需要合并处理
                ByteArrayOutputStream mergedStream = batchPrintToStream(reportInfos, format);
                mergedStream.writeTo(response.getOutputStream());
                response.getOutputStream().flush();
            }
            
            log.info("批量打印成功: 报表数量={}", reportInfos.size());

        } catch (Exception e) {
            log.error("批量打印失败: 报表数量={}", reportInfos.size(), e);
            throw new RuntimeException("批量打印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🎯 批量打印为字节流
     * 用于进一步处理或存储
     * 
     * @param reportInfos 多个报表信息列表
     * @param format 导出格式
     * @return 合并后的字节流
     */
    public ByteArrayOutputStream batchPrintToStream(List<ReportInfo> reportInfos, String format) {
        try {
            log.info("开始批量打印字节流: 报表数量={}, format={}", reportInfos.size(), format);

            if (reportInfos == null || reportInfos.isEmpty()) {
                throw new IllegalArgumentException("报表列表不能为空");
            }

            if (reportInfos.size() == 1) {
                // 单个报表直接返回
                return exportToStream(reportInfos.get(0));
            }

            // 🔑 多个报表合并处理
            // 这里可以根据具体需求实现不同的合并策略
            // 例如：PDF合并、Excel合并等
            
            // 临时实现：返回第一个报表的字节流
            // TODO: 实现真正的报表合并逻辑
            log.warn("多报表合并功能待实现，当前返回第一个报表");
            return exportToStream(reportInfos.get(0));

        } catch (Exception e) {
            log.error("批量打印字节流失败: 报表数量={}", reportInfos.size(), e);
            throw new RuntimeException("批量打印字节流失败: " + e.getMessage(), e);
        }
    }

    // ==================== 🛠️ 内部工具方法 ====================

    /**
     * 准备报表参数
     */
    private Map<String, Object> prepareParameters(ReportInfo reportInfo) {
        Map<String, Object> parameters = new HashMap<>();
        
        // 添加默认参数
        parameters.put("exportTime", new Date());
        
        // 添加自定义参数
        if (reportInfo.getParamMap() != null) {
            parameters.putAll(reportInfo.getParamMap());
        }
        
        // 添加数据集
        if (reportInfo.getDs() != null && !reportInfo.getDs().isEmpty()) {
            for (ReportInfo.DatasetDef datasetDef : reportInfo.getDs()) {
                String datasetName = datasetDef.getDatasetName();
                List<Object> dataset = datasetDef.getDataset();
                parameters.put(datasetName, dataset);
                log.debug("添加数据集: name={}, size={}", datasetName, dataset.size());
            }
        }
        
        return parameters;
    }

    /**
     * 智能模板路径解析
     * 🔑 简化处理：判断协议是本地模式则不使用Redis缓存
     */
    private String resolveTemplatePath(String templateCode) {
        if (templateCode == null || templateCode.trim().isEmpty()) {
            throw new IllegalArgumentException("模板代码不能为空");
        }

        // 如果已经有前缀，直接返回
        if (templateCode.contains(":")) {
            // 🔑 如果是本地协议，强制使用本地模板，避免Redis缓存
            if (templateCode.startsWith("local:")) {
                log.info("🔑 检测到本地协议，强制使用本地模板: {}", templateCode);
                return templateCode;
            }
            return templateCode;
        }

        // 优先检查本地模板
        if (localReportProvider != null && localReportProvider.hasTemplate(templateCode)) {
            log.info("🔑 使用本地模板，避免Redis缓存: {}", templateCode);
            return "local:" + templateCode;
        }

        // 默认使用数据库模板
        log.debug("使用数据库模板: {}", templateCode);
        return "db:" + templateCode;
    }
    



    
    /**
     * 设置HTTP响应头
     */
    private void setResponseHeaders(HttpServletResponse response, String format, String templateCode) {
        String fileName = templateCode;

        switch (format.toLowerCase()) {
            case "pdf":
                response.setContentType("application/pdf");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".pdf");
                break;
            case "excel":
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
                break;
            case "excel97":
                response.setContentType("application/vnd.ms-excel");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xls");
                break;
            case "word":
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".docx");
                break;
            case "html":
                response.setContentType("text/html; charset=UTF-8");
                break;
            default:
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName + "." + format);
        }
        response.setCharacterEncoding("UTF-8");
    }
}