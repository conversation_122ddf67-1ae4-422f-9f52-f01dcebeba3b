<template>
    <div class="conten_body_main margintop10">
        <top-search @query="fun_getTableListData" @reset="fun_searchReset" ref="topSearch">
            <template #otherContion>
                <vxe-form-item :title="$t('统计日期')" :item-render="{}" span="24">
                    <template #default>
                        <el-date-picker
                            size="mini"
                            v-model="searchFormData.summaryDateRange"
                            type="daterange"
                            unlink-panels
                            :range-separator="$t('至')"
                            :start-placeholder="$t('开始日期')"
                            :end-placeholder="$t('结束日期')"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </template>
                </vxe-form-item>
            </template>
            <template #toolBar>
                <vxe-button :content="$t('刷新')" @click="fun_getTableListData"></vxe-button>
                <vxe-button :content="$t('导出')" icon="el-icon-download" @click="fun_exportExcel"></vxe-button>
                <vxe-button :content="$t('打印')" icon="el-icon-printer" @click="fun_printReport"></vxe-button>
                <vxe-toolbar ref="xToolbar" :custom="{icon:'vxe-button--icon vxe-icon-custom-column'}"></vxe-toolbar>
            </template>
        </top-search>
        
        <!-- 概览卡片 -->
        <el-row :gutter="20" class="overview-cards" style="margin-bottom: 20px;">
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="card-content">
                        <div class="card-icon">
                            <i class="el-icon-s-order" style="color: #409EFF;"></i>
                        </div>
                        <div class="card-info">
                            <div class="card-title">{{ $t('总计划数') }}</div>
                            <div class="card-value">{{ overviewData.totalPlans || 0 }}</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="card-content">
                        <div class="card-icon">
                            <i class="el-icon-circle-check" style="color: #67C23A;"></i>
                        </div>
                        <div class="card-info">
                            <div class="card-title">{{ $t('已完成计划') }}</div>
                            <div class="card-value">{{ overviewData.completedPlans || 0 }}</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="card-content">
                        <div class="card-icon">
                            <i class="el-icon-loading" style="color: #E6A23C;"></i>
                        </div>
                        <div class="card-info">
                            <div class="card-title">{{ $t('进行中计划') }}</div>
                            <div class="card-value">{{ overviewData.inProgressPlans || 0 }}</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="card-content">
                        <div class="card-icon">
                            <i class="el-icon-warning" style="color: #F56C6C;"></i>
                        </div>
                        <div class="card-info">
                            <div class="card-title">{{ $t('延期计划') }}</div>
                            <div class="card-value">{{ overviewData.delayedPlans || 0 }}</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        
        <el-container>
            <el-main>
                <!-- 汇总数据表格 -->
                <vxe-table 
                    :loading="loading" 
                    class="mytable-scrollbar" 
                    show-overflow="tooltip" 
                    size="mini" 
                    ref="ListTableRef"  
                    border 
                    stripe 
                    header-align="left"
                    :height="contentStyleObj.height" 
                    :data="tableList" 
                    row-id="id"
                    id="productionPlanExecutionSummaryTableList" 
                    resizable 
                    :custom-config="{storage: true}"
                    :filter-config="{remote:true}" 
                    @filter-change="handleFilterChange"
                    :sort-config="{remote:true}" 
                    @sort-change="handeleSortChange">
                    
                    <vxe-column field="summaryDate" :title="$t('统计日期')" width="120" sortable>
                        <template #default="{row}">{{ formatDate(row.summaryDate) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="workCenterName" :title="$t('工作中心')" min-width="150" :params="{searchType:'text', fuzzySearch: true}" sortable>
                        <template #default="{row}">
                            <span v-if="row.workCenterName && row.workCenterCode">
                                {{ row.workCenterName }}（{{ row.workCenterCode }}）
                            </span>
                            <span v-else-if="row.workCenterName">
                                {{ row.workCenterName }}
                            </span>
                            <span v-else class="text-muted">-</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalPlans" :title="$t('计划总数')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-info">{{ row.totalPlans || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="completedPlans" :title="$t('已完成计划')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.completedPlans || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="inProgressPlans" :title="$t('进行中计划')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-warning">{{ row.inProgressPlans || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="delayedPlans" :title="$t('延期计划')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-danger">{{ row.delayedPlans || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalPlanQty" :title="$t('总计划数量')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-info">{{ row.totalPlanQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalCompletedQty" :title="$t('总完成数量')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.totalCompletedQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalQualifiedQty" :title="$t('总合格数量')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.totalQualifiedQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalDefectiveQty" :title="$t('总不良数量')" width="120" sortable>
                        <template #default="{row}">
                            <span class="text-danger">{{ row.totalDefectiveQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="avgCompletionRate" :title="$t('平均完成率')" width="120" sortable>
                        <template #default="{row}">
                            <span :class="getCompletionRateClass(row.avgCompletionRate)">
                                {{ row.avgCompletionRate || 0 }}%
                            </span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="avgQualifiedRate" :title="$t('平均合格率')" width="120" sortable>
                        <template #default="{row}">
                            <span :class="getQualifiedRateClass(row.avgQualifiedRate)">
                                {{ row.avgQualifiedRate || 0 }}%
                            </span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalTasks" :title="$t('总任务数')" width="100" sortable>
                        <template #default="{row}">
                            {{ row.totalTasks || 0 }}
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="completedTasks" :title="$t('已完成任务')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.completedTasks || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalExceptions" :title="$t('总异常次数')" width="120" sortable>
                        <template #default="{row}">
                            <span v-if="row.totalExceptions > 0" class="text-warning">{{ row.totalExceptions }}</span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="onTimeCompletionRate" :title="$t('按时完成率')" width="120" sortable>
                        <template #default="{row}">
                            <span :class="getCompletionRateClass(row.onTimeCompletionRate)">
                                {{ row.onTimeCompletionRate || 0 }}%
                            </span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="delayRate" :title="$t('延期率')" width="100" sortable>
                        <template #default="{row}">
                            <span :class="getDelayRateClass(row.delayRate)">
                                {{ row.delayRate || 0 }}%
                            </span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="executionEfficiency" :title="$t('执行效率')" width="100" sortable>
                        <template #default="{row}">
                            <span :class="getEfficiencyClass(row.executionEfficiency)">
                                {{ row.executionEfficiency || 0 }}%
                            </span>
                        </template>
                    </vxe-column>
                </vxe-table>
                
                <vxe-pager
                    align="center"
                    size="mini"
                    :current-page.sync="page.current"
                    :page-size.sync="page.size"
                    :total="page.total"
                    perfect
                    background
                    :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                    @page-change="handlePageChange">
                </vxe-pager>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import TopSearch from "@/components/filterate-search/index";
import { getSummaryData, exportExcel } from "@/api/erp/productionPlanExecution";

export default {
    name: "ProductionPlanExecutionSummary",
    components: {
        TopSearch
    },
    data() {
        return {
            loading: false,
            tableList: [],
            searchFormData: {
                summaryDateRange: null
            },
            page: {
                current: 1,
                size: 20,
                total: 0
            },
            contentStyleObj: {
                height: '600px'
            },
            checkList: [],
            overviewData: {
                totalPlans: 0,
                completedPlans: 0,
                inProgressPlans: 0,
                delayedPlans: 0
            }
        };
    },
    mounted() {
        this.fun_getTableListData();
    },
    methods: {
        // 获取表格数据
        fun_getTableListData(filterParam) {
            this.loading = true;

            let pages = {
                page: this.page.current,
                limit: this.page.size
            };

            // 处理自定义日期范围查询
            let searchParams = { ...this.searchFormData };
            if (searchParams.summaryDateRange && searchParams.summaryDateRange.length === 2) {
                searchParams.startDate = searchParams.summaryDateRange[0];
                searchParams.endDate = searchParams.summaryDateRange[1];
                delete searchParams.summaryDateRange;
            }

            let params = Object.assign(pages, searchParams, filterParam, { toUnderLineCase: false });
            Object.assign(this.searchFormData, {}, filterParam);
            
            getSummaryData(params).then(res => {
                const payload = res && res.data ? res.data : {};
                if (payload && (payload.code === 0 || payload.returnCode === '0')) {
                    const rows = Array.isArray(payload.data)
                        ? payload.data
                        : (payload.records || payload.rows || []);
                    this.tableList = rows;
                    this.page.total = payload.total || rows.length || 0;
                    this.calculateOverview();
                } else if (res.status === 200) {
                    this.tableList = payload.records || payload.rows || payload.data || [];
                    this.page.total = payload.total || this.tableList.length || 0;
                    this.calculateOverview();
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },
        
        // 计算概览数据
        calculateOverview() {
            this.overviewData = {
                totalPlans: this.tableList.reduce((sum, item) => sum + (item.totalPlans || 0), 0),
                completedPlans: this.tableList.reduce((sum, item) => sum + (item.completedPlans || 0), 0),
                inProgressPlans: this.tableList.reduce((sum, item) => sum + (item.inProgressPlans || 0), 0),
                delayedPlans: this.tableList.reduce((sum, item) => sum + (item.delayedPlans || 0), 0)
            };
        },
        
        // 搜索重置
        fun_searchReset() {
            this.searchFormData = {
                summaryDateRange: null
            };
            this.page.current = 1;
            this.fun_getTableListData();
        },
        
        // 分页变化
        handlePageChange({ currentPage, pageSize }) {
            this.page.current = currentPage;
            this.page.size = pageSize;
            this.fun_getTableListData();
        },
        
        // 排序变化
        handeleSortChange({ property, order }) {
            let sort = { field: property, order: order };
            this.fun_getTableListData(sort);
        },
        
        // 筛选变化
        handleFilterChange({ filters }) {
            let filterParamArray = [];
            filters.forEach(item => {
                filterParamArray.push(item.datas[0].filterSos);
            });
            if (filterParamArray.length > 0) {
                this.fun_getTableListData({ filterSos: JSON.stringify(filterParamArray) });
            } else {
                this.fun_getTableListData({ filterSos: [] });
            }
        },
        
        // 导出Excel
        fun_exportExcel() {
            let exportParams = { ...this.searchFormData };
            if (exportParams.summaryDateRange && exportParams.summaryDateRange.length === 2) {
                exportParams.startDate = exportParams.summaryDateRange[0];
                exportParams.endDate = exportParams.summaryDateRange[1];
                delete exportParams.summaryDateRange;
            }

            const params = Object.assign({}, exportParams, { toUnderLineCase: false });
            exportExcel(params).then(() => {
                this.$message.success(this.$t('导出成功'));
            }).catch(error => {
                this.$message.error(this.$t('导出失败'));
                console.error('Export error:', error);
            });
        },
        
        // 打印报表
        fun_printReport() {
            window.print();
        },

        // 格式化时间
        formatTime(timestr) {
            try {
                if (timestr) {
                    return timestr.substr(0, 10);
                } else {
                    return '';
                }
            } catch (e) {
                return '';
            }
        },
        
        // 格式化日期
        formatDate(dateStr) {
            return this.formatTime(dateStr);
        },
        
        // 获取完成率样式类
        getCompletionRateClass(rate) {
            if (rate >= 100) return 'text-success';
            if (rate >= 80) return 'text-warning';
            return 'text-danger';
        },
        
        // 获取合格率样式类
        getQualifiedRateClass(rate) {
            if (rate >= 95) return 'text-success';
            if (rate >= 85) return 'text-warning';
            return 'text-danger';
        },
        
        // 获取延期率样式类
        getDelayRateClass(rate) {
            if (rate <= 5) return 'text-success';
            if (rate <= 15) return 'text-warning';
            return 'text-danger';
        },
        
        // 获取执行效率样式类
        getEfficiencyClass(rate) {
            if (rate >= 90) return 'text-success';
            if (rate >= 70) return 'text-warning';
            return 'text-danger';
        }
    }
};
</script>

<style scoped>
.text-primary {
    color: #409EFF;
}

.text-success {
    color: #67C23A;
}

.text-warning {
    color: #E6A23C;
}

.text-danger {
    color: #F56C6C;
}

.text-info {
    color: #909399;
}

.text-muted {
    color: #C0C4CC;
}

.mytable-scrollbar {
    margin-top: 20px;
}

/* 概览卡片样式 */
.overview-cards {
    margin-bottom: 20px;
}

.overview-card {
    height: 110px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
}

.overview-card .el-card__body {
    padding: 15px;
    height: 100%;
}

.card-content {
    display: flex;
    align-items: center;
    height: 100%;
}

.card-icon {
    font-size: 32px;
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.card-info {
    flex: 1;
}

.card-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    line-height: 1.2;
}

.card-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    line-height: 1.2;
}

/* 表格样式调整 - 保持与现有功能一致 */
::v-deep .vxe-table .vxe-body--column {
    padding: 12px 8px;
    line-height: 1.4;
}
</style>
