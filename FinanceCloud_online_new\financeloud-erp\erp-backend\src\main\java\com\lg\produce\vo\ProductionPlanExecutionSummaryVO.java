package com.lg.produce.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 生产计划执行情况汇总报表VO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel(description = "生产计划执行情况汇总报表")
public class ProductionPlanExecutionSummaryVO {

    @ApiModelProperty("统计日期")
    private LocalDate summaryDate;

    @ApiModelProperty("工作中心ID")
    private Long workCenterId;

    @ApiModelProperty("工作中心编码")
    private String workCenterCode;

    @ApiModelProperty("工作中心名称")
    private String workCenterName;

    @ApiModelProperty("计划总数")
    private Integer totalPlans;

    @ApiModelProperty("已完成计划数")
    private Integer completedPlans;

    @ApiModelProperty("进行中计划数")
    private Integer inProgressPlans;

    @ApiModelProperty("延期计划数")
    private Integer delayedPlans;

    @ApiModelProperty("总计划数量")
    private BigDecimal totalPlanQty;

    @ApiModelProperty("总完成数量")
    private BigDecimal totalCompletedQty;

    @ApiModelProperty("总合格数量")
    private BigDecimal totalQualifiedQty;

    @ApiModelProperty("总不良数量")
    private BigDecimal totalDefectiveQty;

    @ApiModelProperty("平均完成率")
    private BigDecimal avgCompletionRate;

    @ApiModelProperty("平均合格率")
    private BigDecimal avgQualifiedRate;

    @ApiModelProperty("总任务数")
    private Integer totalTasks;

    @ApiModelProperty("已完成任务数")
    private Integer completedTasks;

    @ApiModelProperty("总异常次数")
    private Integer totalExceptions;

    @ApiModelProperty("按时完成率")
    private BigDecimal onTimeCompletionRate;

    @ApiModelProperty("延期率")
    private BigDecimal delayRate;

    @ApiModelProperty("计划执行效率")
    private BigDecimal executionEfficiency;
}
