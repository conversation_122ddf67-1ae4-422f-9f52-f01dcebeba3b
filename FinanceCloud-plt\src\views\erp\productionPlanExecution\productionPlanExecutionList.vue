<template>
    <div class="conten_body_main margintop10">
        <top-search @query="fun_getTableListData" @reset="fun_searchReset" ref="topSearch">
            <template #otherContion>
                <vxe-form-item :title="$t('计划日期')" :item-render="{}" span="24">
                    <template #default>
                        <el-date-picker
                            size="mini"
                            v-model="searchFormData.planDateRange"
                            type="daterange"
                            unlink-panels
                            :range-separator="$t('至')"
                            :start-placeholder="$t('开始日期')"
                            :end-placeholder="$t('结束日期')"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </template>
                </vxe-form-item>
            </template>
            <template #toolBar>
                <vxe-button :content="$t('刷新')" @click="fun_getTableListData"></vxe-button>
                <vxe-button :content="$t('导出')" icon="el-icon-download" @click="fun_exportExcel"></vxe-button>
                <vxe-button :content="$t('打印')" icon="el-icon-printer" @click="fun_printReport"></vxe-button>
                <vxe-toolbar ref="xToolbar" :custom="{icon:'vxe-button--icon vxe-icon-custom-column'}"></vxe-toolbar>
            </template>
        </top-search>
        
        <el-container>
            <el-main>
                <!-- 详细数据表格 -->
                <vxe-table 
                    :loading="loading" 
                    class="mytable-scrollbar" 
                    show-overflow="tooltip" 
                    size="mini" 
                    ref="ListTableRef"  
                    border 
                    stripe 
                    header-align="left"
                    :height="contentStyleObj.height" 
                    :data="tableList" 
                    row-id="id"
                    id="productionPlanExecutionTableList" 
                    resizable 
                    :custom-config="{storage: true}"
                    :filter-config="{remote:true}" 
                    @filter-change="handleFilterChange"
                    :sort-config="{remote:true}" 
                    @sort-change="handeleSortChange">
                    
                    <vxe-column field="planDate" :title="$t('计划日期')" width="120" sortable>
                        <template #default="{row}">{{ formatDate(row.planDate) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="planCode" :title="$t('计划编号')" min-width="150" :params="{searchType:'text', fuzzySearch: true}" sortable>
                        <template #default="{row}">
                            <span class="text-primary">{{ row.planCode || '-' }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="planName" :title="$t('计划名称')" min-width="200" :params="{searchType:'text', fuzzySearch: true}">
                        <template #default="{row}">
                            {{ row.planName || '-' }}
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="workCenterName" :title="$t('工作中心')" min-width="150" :params="{searchType:'text', fuzzySearch: true}" sortable>
                        <template #default="{row}">
                            <span v-if="row.workCenterName && row.workCenterCode">
                                {{ row.workCenterName }}（{{ row.workCenterCode }}）
                            </span>
                            <span v-else-if="row.workCenterName">
                                {{ row.workCenterName }}
                            </span>
                            <span v-else class="text-muted">-</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="materialName" :title="$t('物料信息')" min-width="200" :params="{searchType:'text', fuzzySearch: true}">
                        <template #default="{row}">
                            <span v-if="row.materialName && row.materialCode">
                                {{ row.materialName }}（{{ row.materialCode }}）
                            </span>
                            <span v-else-if="row.materialName">
                                {{ row.materialName }}
                            </span>
                            <span v-else class="text-muted">-</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="planQty" :title="$t('计划数量')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-info">{{ row.planQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="completedQty" :title="$t('完成数量')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.completedQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="qualifiedQty" :title="$t('合格数量')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.qualifiedQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="defectiveQty" :title="$t('不良数量')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-danger">{{ row.defectiveQty || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="completionRate" :title="$t('完成率')" width="100" sortable>
                        <template #default="{row}">
                            <span :class="getCompletionRateClass(row.completionRate)">
                                {{ row.completionRate || 0 }}%
                            </span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="qualifiedRate" :title="$t('合格率')" width="100" sortable>
                        <template #default="{row}">
                            <span :class="getQualifiedRateClass(row.qualifiedRate)">
                                {{ row.qualifiedRate || 0 }}%
                            </span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="planStatusName" :title="$t('计划状态')" width="100" sortable>
                        <template #default="{row}">
                            <el-tag :type="getPlanStatusType(row.planStatus)" size="mini">
                                {{ row.planStatusName || '-' }}
                            </el-tag>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="executionStatusName" :title="$t('执行状态')" width="100" sortable>
                        <template #default="{row}">
                            <el-tag :type="getExecutionStatusType(row.executionStatus)" size="mini">
                                {{ row.executionStatusName || '-' }}
                            </el-tag>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="delayDays" :title="$t('延期天数')" width="100" sortable>
                        <template #default="{row}">
                            <span v-if="row.delayDays > 0" class="text-danger">{{ row.delayDays }}</span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="taskCount" :title="$t('任务数')" width="80" sortable>
                        <template #default="{row}">
                            {{ row.taskCount || 0 }}
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="completedTaskCount" :title="$t('已完成任务')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.completedTaskCount || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="exceptionCount" :title="$t('异常次数')" width="100" sortable>
                        <template #default="{row}">
                            <span v-if="row.exceptionCount > 0" class="text-warning">{{ row.exceptionCount }}</span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="responsiblePerson" :title="$t('负责人')" width="120" :params="{searchType:'text', fuzzySearch: true}">
                        <template #default="{row}">
                            {{ row.responsiblePerson || '-' }}
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="planStartTime" :title="$t('计划开始时间')" width="150">
                        <template #default="{row}">{{ formatDateTime(row.planStartTime) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="planEndTime" :title="$t('计划结束时间')" width="150">
                        <template #default="{row}">{{ formatDateTime(row.planEndTime) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="actualStartTime" :title="$t('实际开始时间')" width="150">
                        <template #default="{row}">{{ formatDateTime(row.actualStartTime) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="actualEndTime" :title="$t('实际结束时间')" width="150">
                        <template #default="{row}">{{ formatDateTime(row.actualEndTime) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="exceptionTypes" :title="$t('异常类型')" min-width="150" show-overflow="tooltip">
                        <template #default="{row}">
                            <span v-if="row.exceptionTypes" class="text-warning">{{ row.exceptionTypes }}</span>
                            <span v-else class="text-muted">-</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="remarks" :title="$t('备注')" min-width="150" show-overflow="tooltip">
                        <template #default="{row}">
                            {{ row.remarks || '-' }}
                        </template>
                    </vxe-column>
                </vxe-table>
                
                <vxe-pager
                    align="center"
                    size="mini"
                    :current-page.sync="page.current"
                    :page-size.sync="page.size"
                    :total="page.total"
                    perfect
                    background
                    :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                    @page-change="handlePageChange">
                </vxe-pager>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import TopSearch from "@/components/filterate-search/index";
import { getTableListData, exportExcel } from "@/api/erp/productionPlanExecution";

export default {
    name: "ProductionPlanExecutionList",
    components: {
        TopSearch
    },
    data() {
        return {
            loading: false,
            tableList: [],
            searchFormData: {
                planDateRange: null
            },
            page: {
                current: 1,
                size: 20,
                total: 0
            },
            contentStyleObj: {
                height: '600px'
            },
            checkList: []
        };
    },
    mounted() {
        this.fun_getTableListData();
    },
    methods: {
        // 获取表格数据
        fun_getTableListData(filterParam) {
            this.loading = true;

            let pages = {
                page: this.page.current,
                limit: this.page.size
            };

            // 处理自定义日期范围查询
            let searchParams = { ...this.searchFormData };
            if (searchParams.planDateRange && searchParams.planDateRange.length === 2) {
                searchParams.startDate = searchParams.planDateRange[0];
                searchParams.endDate = searchParams.planDateRange[1];
                delete searchParams.planDateRange;
            }

            let params = Object.assign(pages, searchParams, filterParam, { toUnderLineCase: false });
            Object.assign(this.searchFormData, {}, filterParam);
            
            getTableListData(params).then(res => {
                const payload = res && res.data ? res.data : {};
                if (payload && (payload.code === 0 || payload.returnCode === '0')) {
                    const rows = Array.isArray(payload.data)
                        ? payload.data
                        : (payload.records || payload.rows || []);
                    this.tableList = rows;
                    this.page.total = payload.total || rows.length || 0;
                } else if (res.status === 200) {
                    this.tableList = payload.records || payload.rows || payload.data || [];
                    this.page.total = payload.total || this.tableList.length || 0;
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },
        
        // 搜索重置
        fun_searchReset() {
            this.searchFormData = {
                planDateRange: null
            };
            this.page.current = 1;
            this.fun_getTableListData();
        },
        
        // 分页变化
        handlePageChange({ currentPage, pageSize }) {
            this.page.current = currentPage;
            this.page.size = pageSize;
            this.fun_getTableListData();
        },
        
        // 排序变化
        handeleSortChange({ property, order }) {
            let sort = { field: property, order: order };
            this.fun_getTableListData(sort);
        },
        
        // 筛选变化
        handleFilterChange({ filters }) {
            let filterParamArray = [];
            filters.forEach(item => {
                filterParamArray.push(item.datas[0].filterSos);
            });
            if (filterParamArray.length > 0) {
                this.fun_getTableListData({ filterSos: JSON.stringify(filterParamArray) });
            } else {
                this.fun_getTableListData({ filterSos: [] });
            }
        },
        
        // 导出Excel
        fun_exportExcel() {
            let exportParams = { ...this.searchFormData };
            if (exportParams.planDateRange && exportParams.planDateRange.length === 2) {
                exportParams.startDate = exportParams.planDateRange[0];
                exportParams.endDate = exportParams.planDateRange[1];
                delete exportParams.planDateRange;
            }

            const params = Object.assign({}, exportParams, { toUnderLineCase: false });
            exportExcel(params).then(() => {
                this.$message.success(this.$t('导出成功'));
            }).catch(error => {
                this.$message.error(this.$t('导出失败'));
                console.error('Export error:', error);
            });
        },
        
        // 打印报表
        fun_printReport() {
            window.print();
        },

        // 格式化时间
        formatTime(timestr) {
            try {
                if (timestr) {
                    return timestr.substr(0, 10);
                } else {
                    return '';
                }
            } catch (e) {
                return '';
            }
        },
        
        // 格式化日期
        formatDate(dateStr) {
            return this.formatTime(dateStr);
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            return this.formatTime(dateTime);
        },
        
        // 获取完成率样式类
        getCompletionRateClass(rate) {
            if (rate >= 100) return 'text-success';
            if (rate >= 80) return 'text-warning';
            return 'text-danger';
        },
        
        // 获取合格率样式类
        getQualifiedRateClass(rate) {
            if (rate >= 95) return 'text-success';
            if (rate >= 85) return 'text-warning';
            return 'text-danger';
        },
        
        // 获取计划状态标签类型
        getPlanStatusType(status) {
            switch (status) {
                case '0': return 'info';      // 未开始
                case '1': return 'warning';   // 进行中
                case '2': return 'success';   // 已完成
                case '3': return 'danger';    // 已暂停
                case '4': return 'info';      // 已取消
                default: return 'info';
            }
        },
        
        // 获取执行状态标签类型
        getExecutionStatusType(status) {
            switch (status) {
                case '已完成': return 'success';
                case '进行中': return 'warning';
                case '未开始': return 'info';
                case '已暂停': return 'danger';
                case '已取消': return 'info';
                default: return 'info';
            }
        }
    }
};
</script>

<style scoped>
.text-primary {
    color: #409EFF;
}

.text-success {
    color: #67C23A;
}

.text-warning {
    color: #E6A23C;
}

.text-danger {
    color: #F56C6C;
}

.text-info {
    color: #909399;
}

.text-muted {
    color: #C0C4CC;
}

.mytable-scrollbar {
    margin-top: 20px;
}

/* 表格样式调整 - 保持与现有功能一致 */
::v-deep .vxe-table .vxe-body--column {
    padding: 12px 8px;
    line-height: 1.4;
}
</style>
