package com.lg.produce.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 生产日报汇总VO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel(description = "生产日报汇总VO")
public class ProductionDailySummaryVO {
    
    @ApiModelProperty("报表日期")
    private LocalDate reportDate;
    
    @ApiModelProperty("工作中心数量")
    private Integer workCenterCount;
    
    @ApiModelProperty("操作员数量")
    private Integer operatorCount;
    
    @ApiModelProperty("设备数量")
    private Integer equipmentCount;
    
    @ApiModelProperty("总任务数")
    private Integer totalTasks;
    
    @ApiModelProperty("总完成数量")
    private BigDecimal totalCompletedQty;
    
    @ApiModelProperty("总合格数量")
    private BigDecimal totalQualifiedQty;
    
    @ApiModelProperty("总不良数量")
    private BigDecimal totalDefectiveQty;
    
    @ApiModelProperty("整体合格率")
    private BigDecimal overallQualifiedRate;
    
    @ApiModelProperty("整体不良率")
    private BigDecimal overallDefectiveRate;
    
    @ApiModelProperty("总异常次数")
    private Integer totalExceptions;
    
    @ApiModelProperty("总停机事件数")
    private Integer totalDowntimeEvents;
    
    @ApiModelProperty("总停机时长")
    private BigDecimal totalDowntimeDuration;
    
    @ApiModelProperty("总设备工时")
    private BigDecimal totalEquipmentHours;
    
    @ApiModelProperty("总人员工时")
    private BigDecimal totalStaffHours;
    
    @ApiModelProperty("平均任务时长（分钟）")
    private BigDecimal avgTaskDurationMinutes;
    
    @ApiModelProperty("平均每个工作中心产出")
    private BigDecimal avgOutputPerWorkCenter;
    
    @ApiModelProperty("平均每台设备产出")
    private BigDecimal avgOutputPerEquipment;
}
