package com.lg.produce.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 生产日报VO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel(description = "生产日报VO")
public class ProductionDailyReportVO {
    
    @ApiModelProperty("报表日期")
    private LocalDate reportDate;
    
    @ApiModelProperty("工作中心名称")
    private String workCenterName;
    
    @ApiModelProperty("工作中心编码")
    private String workCenterCode;
    
    @ApiModelProperty("物料名称")
    private String materialName;
    
    @ApiModelProperty("物料编码")
    private String materialCode;
    
    @ApiModelProperty("物料型号")
    private String materialModel;
    
    @ApiModelProperty("操作员姓名")
    private String operatorName;
    
    @ApiModelProperty("设备信息")
    private String equipmentInfo;
    
    @ApiModelProperty("设备名称")
    private String equipmentNames;
    
    @ApiModelProperty("任务数量")
    private Integer taskCount;
    
    @ApiModelProperty("总完成数量")
    private BigDecimal totalCompletedQty;
    
    @ApiModelProperty("总合格数量")
    private BigDecimal totalQualifiedQty;
    
    @ApiModelProperty("总不良数量")
    private BigDecimal totalDefectiveQty;
    
    @ApiModelProperty("合格率")
    private BigDecimal qualifiedRate;
    
    @ApiModelProperty("不良率")
    private BigDecimal defectiveRate;
    
    @ApiModelProperty("异常次数")
    private Integer exceptionCount;
    
    @ApiModelProperty("停机次数")
    private Integer downtimeCount;
    
    @ApiModelProperty("总停机时长")
    private BigDecimal totalDowntimeDuration;
    
    @ApiModelProperty("总设备工时")
    private BigDecimal totalEquipmentHours;
    
    @ApiModelProperty("总人员工时")
    private BigDecimal totalStaffHours;
    
    @ApiModelProperty("平均任务时长（分钟）")
    private BigDecimal avgTaskDurationMinutes;
    
    @ApiModelProperty("异常类型")
    private String exceptionTypes;
    
    @ApiModelProperty("停机原因")
    private String downtimeReasons;
    
    @ApiModelProperty("首次任务开始时间")
    private LocalDateTime firstTaskStart;
    
    @ApiModelProperty("最后任务结束时间")
    private LocalDateTime lastTaskEnd;
}
